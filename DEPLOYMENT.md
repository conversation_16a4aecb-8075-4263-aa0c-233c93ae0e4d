# Production Deployment Guide

This guide explains how to deploy the Beauty Website frontend to various hosting platforms with proper React Router support.

## Build the Project

First, build the project for production:

```bash
npm run build
```

This creates a `dist` folder with the production build.

## Deployment Options

### 1. Netlify (Recommended)

**Files included:** `public/_redirects`

1. Upload the `dist` folder to Netlify
2. The `_redirects` file will automatically handle client-side routing
3. All routes like `/products`, `/services` will work correctly

**Deploy command:**
```bash
npm run build
```

**Publish directory:** `dist`

### 2. Vercel

**Files included:** `vercel.json`

1. Install Vercel CLI: `npm i -g vercel`
2. Run: `vercel --prod`
3. The `vercel.json` file handles routing automatically

### 3. GitHub Pages

**Files included:** `public/404.html` and script in `index.html`

1. Build the project: `npm run build`
2. Push the `dist` folder contents to your `gh-pages` branch
3. The 404.html and redirect script handle client-side routing

### 4. Apache Server

**Files included:** `public/.htaccess`

1. Upload the `dist` folder contents to your Apache server
2. The `.htaccess` file will handle URL rewriting
3. Ensure `mod_rewrite` is enabled on your server

### 5. Nginx Server

Add this to your Nginx configuration:

```nginx
location / {
    try_files $uri $uri/ /index.html;
}
```

## Environment Variables

For production, create a `.env.production` file:

```env
VITE_API_URL=https://your-backend-domain.com/api
```

## Build Optimization

The build is optimized with:
- Code splitting for vendor libraries
- Asset optimization
- Tree shaking for unused code
- Minification

## Troubleshooting

### 404 Errors on Direct Navigation

If you still get 404 errors when navigating directly to routes:

1. **Check server configuration** - Ensure your hosting platform serves `index.html` for all routes
2. **Verify build files** - Make sure the redirect files are in the correct location
3. **Clear cache** - Clear browser cache and CDN cache if applicable

### API Connection Issues

1. Update `VITE_API_URL` in your environment variables
2. Ensure CORS is properly configured on your backend
3. Check that your API endpoints are accessible from the production domain

## Performance Tips

1. **Enable gzip compression** on your server
2. **Set proper cache headers** for static assets
3. **Use a CDN** for better global performance
4. **Monitor bundle size** with `npm run build` output

## Security Considerations

1. **HTTPS only** - Always use HTTPS in production
2. **Environment variables** - Never commit sensitive data to git
3. **CSP headers** - Consider implementing Content Security Policy
4. **Regular updates** - Keep dependencies updated

## Post-Deployment Checklist

- [ ] All routes work correctly (/, /products, /services, etc.)
- [ ] API calls are successful
- [ ] Images and assets load properly
- [ ] Mobile responsiveness works
- [ ] SEO meta tags are correct
- [ ] Performance is acceptable (< 3s load time)
