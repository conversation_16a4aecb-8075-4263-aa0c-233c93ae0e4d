# Beauty Parlour Website - Django Backend Documentation

## Project Overview

This is a comprehensive beauty parlour website with a React frontend and Django REST API backend. The system manages products, services, bookings, testimonials, and customer interactions for a luxury beauty parlour business.

## Technology Stack

### Frontend
- **React.js** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** with custom design system
- **shadcn-ui** components
- **React Router** for navigation
- **React Query** for API state management

### Backend (Django)
- **Django 4.x** with Django REST Framework
- **PostgreSQL** database (recommended for production)
- **SQLite** for development
- **Django Admin** for content management
- **Pillow** for image processing
- **django-cors-headers** for CORS handling

## Database Models Documentation

### 1. Product Model

The Product model represents beauty products available for purchase.

```python
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator

class Product(models.Model):
    CATEGORY_CHOICES = [
        ('skincare', 'Skincare'),
        ('makeup', 'Makeup'),
        ('haircare', 'Hair Care'),
        ('tools', 'Beauty Tools'),
        ('fragrance', 'Fragrance'),
    ]
    
    # Basic Information
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200, help_text="Product name")
    description = models.TextField(help_text="Detailed product description")
    price = models.DecimalField(max_digits=10, decimal_places=2, help_text="Product price in INR")
    
    # Product Details
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, help_text="Product category")
    rating = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(5.0)],
        default=0.0,
        help_text="Average rating (0-5 stars)"
    )
    in_stock = models.BooleanField(default=True, help_text="Product availability status")
    
    # Additional Information
    ingredients = models.JSONField(blank=True, null=True, help_text="List of ingredients")
    usage_instructions = models.TextField(blank=True, help_text="How to use the product")
    benefits = models.JSONField(blank=True, null=True, help_text="List of product benefits")
    
    # Media
    image = models.ImageField(upload_to='products/', help_text="Main product image")
    additional_images = models.JSONField(blank=True, null=True, help_text="Additional product images URLs")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True, help_text="Product visibility status")
    
    class Meta:
        db_table = 'products'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['category']),
            models.Index(fields=['in_stock']),
            models.Index(fields=['rating']),
        ]
    
    def __str__(self):
        return self.name
```

### 2. Service Category Model

Represents different categories of services offered.

```python
class ServiceCategory(models.Model):
    id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=100, unique=True, help_text="Category name")
    description = models.TextField(blank=True, help_text="Category description")
    icon = models.CharField(max_length=50, blank=True, help_text="Icon class name")
    display_order = models.IntegerField(default=0, help_text="Display order on website")
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'service_categories'
        ordering = ['display_order', 'title']
        verbose_name_plural = "Service Categories"
    
    def __str__(self):
        return self.title
```

### 3. Service Model

Represents individual beauty services offered by the parlour.

```python
class Service(models.Model):
    # Basic Information
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200, help_text="Service name")
    description = models.TextField(help_text="Service description")
    category = models.ForeignKey(
        ServiceCategory, 
        on_delete=models.CASCADE, 
        related_name='services',
        help_text="Service category"
    )
    
    # Service Details
    duration = models.CharField(max_length=50, help_text="Service duration (e.g., '90 minutes')")
    price = models.DecimalField(max_digits=10, decimal_places=2, help_text="Service price in INR")
    popular = models.BooleanField(default=False, help_text="Mark as popular service")
    
    # Detailed Information
    process_steps = models.JSONField(blank=True, null=True, help_text="Step-by-step service process")
    benefits = models.JSONField(blank=True, null=True, help_text="Service benefits list")
    equipment_used = models.JSONField(blank=True, null=True, help_text="Equipment and tools used")
    staff_qualification = models.TextField(blank=True, help_text="Staff expertise information")
    
    # FAQ
    faq = models.JSONField(blank=True, null=True, help_text="Frequently asked questions")
    
    # Media
    image = models.ImageField(upload_to='services/', help_text="Main service image")
    before_after_images = models.JSONField(blank=True, null=True, help_text="Before/after image pairs")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'services'
        ordering = ['-popular', 'name']
        indexes = [
            models.Index(fields=['category']),
            models.Index(fields=['popular']),
            models.Index(fields=['price']),
        ]
    
    def __str__(self):
        return f"{self.name} - {self.category.title}"
```

### 4. Testimonial Model

Stores customer reviews and testimonials.

```python
class Testimonial(models.Model):
    RATING_CHOICES = [(i, i) for i in range(1, 6)]
    
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100, help_text="Customer name")
    email = models.EmailField(blank=True, help_text="Customer email (optional)")
    rating = models.IntegerField(choices=RATING_CHOICES, help_text="Rating (1-5 stars)")
    comment = models.TextField(help_text="Customer review/testimonial")
    
    # Service Information
    service_name = models.CharField(max_length=200, help_text="Service reviewed")
    service = models.ForeignKey(
        Service, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='testimonials'
    )
    
    # Media
    customer_image = models.ImageField(
        upload_to='testimonials/', 
        blank=True, 
        null=True,
        help_text="Customer photo (optional)"
    )
    
    # Status
    is_approved = models.BooleanField(default=False, help_text="Admin approval status")
    is_featured = models.BooleanField(default=False, help_text="Feature on homepage")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'testimonials'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['rating']),
            models.Index(fields=['is_approved']),
            models.Index(fields=['is_featured']),
        ]
    
    def __str__(self):
        return f"{self.name} - {self.rating} stars"
```

### 5. Gallery Image Model

Manages gallery images for showcasing work.

```python
class GalleryImage(models.Model):
    CATEGORY_CHOICES = [
        ('makeup', 'Makeup'),
        ('hair', 'Hair'),
        ('facial', 'Facial'),
        ('bridal', 'Bridal'),
        ('before_after', 'Before & After'),
    ]
    
    id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=200, blank=True, help_text="Image title")
    description = models.TextField(blank=True, help_text="Image description")
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    
    # Images
    image = models.ImageField(upload_to='gallery/', help_text="Gallery image")
    thumbnail = models.ImageField(upload_to='gallery/thumbnails/', blank=True, null=True)
    
    # Before/After specific fields
    before_image = models.ImageField(upload_to='gallery/before/', blank=True, null=True)
    after_image = models.ImageField(upload_to='gallery/after/', blank=True, null=True)
    
    # Service Association
    related_service = models.ForeignKey(
        Service, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='gallery_images'
    )
    
    # Display Settings
    is_featured = models.BooleanField(default=False)
    display_order = models.IntegerField(default=0)
    is_active = models.BooleanField(default=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'gallery_images'
        ordering = ['display_order', '-created_at']
        indexes = [
            models.Index(fields=['category']),
            models.Index(fields=['is_featured']),
        ]
    
    def __str__(self):
        return self.title or f"Gallery Image {self.id}"
```

### 6. Contact Form Model

Stores contact form submissions.

```python
class ContactSubmission(models.Model):
    STATUS_CHOICES = [
        ('new', 'New'),
        ('in_progress', 'In Progress'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
    ]
    
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100, help_text="Customer name")
    email = models.EmailField(help_text="Customer email")
    phone = models.CharField(max_length=15, help_text="Customer phone number")
    service_interest = models.CharField(max_length=200, blank=True, help_text="Service of interest")
    message = models.TextField(help_text="Customer message")
    
    # Status & Assignment
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='new')
    assigned_to = models.ForeignKey(
        'auth.User', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        help_text="Staff member assigned to handle this inquiry"
    )
    
    # Response
    admin_notes = models.TextField(blank=True, help_text="Internal notes")
    response_sent = models.BooleanField(default=False)
    response_date = models.DateTimeField(null=True, blank=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'contact_submissions'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['email']),
        ]
    
    def __str__(self):
        return f"{self.name} - {self.status}"
```

### 7. Booking/Appointment Model

Manages service bookings and appointments.

```python
class Booking(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('no_show', 'No Show'),
    ]
    
    id = models.AutoField(primary_key=True)
    booking_reference = models.CharField(max_length=20, unique=True, help_text="Unique booking reference")
    
    # Customer Information
    customer_name = models.CharField(max_length=100, help_text="Customer name")
    customer_email = models.EmailField(help_text="Customer email")
    customer_phone = models.CharField(max_length=15, help_text="Customer phone")
    
    # Service Information
    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name='bookings')
    service_price = models.DecimalField(max_digits=10, decimal_places=2, help_text="Service price at booking time")
    
    # Appointment Details
    preferred_date = models.DateField(help_text="Preferred appointment date")
    preferred_time = models.TimeField(help_text="Preferred appointment time")
    actual_date = models.DateField(null=True, blank=True, help_text="Confirmed appointment date")
    actual_time = models.TimeField(null=True, blank=True, help_text="Confirmed appointment time")
    duration_minutes = models.IntegerField(default=60, help_text="Appointment duration in minutes")
    
    # Status & Notes
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    customer_notes = models.TextField(blank=True, help_text="Customer special requests")
    admin_notes = models.TextField(blank=True, help_text="Internal staff notes")
    
    # Staff Assignment
    assigned_staff = models.ForeignKey(
        'auth.User', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='assigned_bookings'
    )
    
    # Payment Information
    payment_status = models.CharField(
        max_length=20, 
        choices=[('pending', 'Pending'), ('partial', 'Partial'), ('paid', 'Paid')],
        default='pending'
    )
    payment_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'bookings'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['preferred_date']),
            models.Index(fields=['customer_email']),
            models.Index(fields=['booking_reference']),
        ]
    
    def save(self, *args, **kwargs):
        if not self.booking_reference:
            import uuid
            self.booking_reference = f"BK{uuid.uuid4().hex[:8].upper()}"
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.booking_reference} - {self.customer_name}"
```

## API Endpoints Documentation

### Products API
- `GET /api/products/` - List all products with filtering
- `GET /api/products/{id}/` - Get product details
- `POST /api/products/` - Create product (admin only)
- `PUT /api/products/{id}/` - Update product (admin only)
- `DELETE /api/products/{id}/` - Delete product (admin only)

### Services API
- `GET /api/services/` - List all services by category
- `GET /api/services/{id}/` - Get service details
- `GET /api/service-categories/` - List service categories
- `POST /api/services/` - Create service (admin only)

### Testimonials API
- `GET /api/testimonials/` - List approved testimonials
- `POST /api/testimonials/` - Submit testimonial
- `GET /api/testimonials/featured/` - Get featured testimonials

### Gallery API
- `GET /api/gallery/` - List gallery images with category filtering
- `GET /api/gallery/{id}/` - Get gallery image details

### Contact API
- `POST /api/contact/` - Submit contact form
- `GET /api/contact/{id}/` - Get contact submission (admin only)

### Booking API
- `POST /api/bookings/` - Create booking
- `GET /api/bookings/{reference}/` - Get booking details
- `PUT /api/bookings/{reference}/` - Update booking status (admin only)

## Installation & Setup Instructions

### Backend Setup

1. **Clone Repository**
```bash
git clone <repository-url>
cd beauty-parlour-backend
```

2. **Virtual Environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install Dependencies**
```bash
pip install -r requirements.txt
```

4. **Environment Configuration**
Create `.env` file:
```env
DEBUG=True
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///db.sqlite3
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ALLOWED_ORIGINS=http://localhost:5173,http://127.0.0.1:5173

# For production
# DATABASE_URL=postgresql://user:password@localhost/dbname
# CLOUDINARY_CLOUD_NAME=your-cloud-name
# CLOUDINARY_API_KEY=your-api-key
# CLOUDINARY_API_SECRET=your-api-secret
```

5. **Database Migration**
```bash
python manage.py makemigrations
python manage.py migrate
```

6. **Create Superuser**
```bash
python manage.py createsuperuser
```

7. **Load Sample Data**
```bash
python manage.py loaddata fixtures/sample_data.json
```

8. **Run Development Server**
```bash
python manage.py runserver
```

### Frontend Setup

1. **Install Dependencies**
```bash
npm install
```

2. **Environment Configuration**
Create `.env.local`:
```env
REACT_APP_API_URL=http://localhost:8000/api
```

3. **Run Development Server**
```bash
npm run dev
```

## Admin Panel Setup

1. Access admin panel at `http://localhost:8000/admin/`
2. Login with superuser credentials
3. Configure:
   - Service categories
   - Services with detailed information
   - Products with images
   - Gallery images
   - Featured testimonials

## Image Upload Handling

### Local Development
- Images stored in `media/` directory
- Served by Django development server
- Configure `MEDIA_URL` and `MEDIA_ROOT` in settings

### Production (Recommended: Cloudinary)
```python
# settings.py
import cloudinary
import cloudinary.uploader
import cloudinary.api

cloudinary.config(
    cloud_name=os.getenv('CLOUDINARY_CLOUD_NAME'),
    api_key=os.getenv('CLOUDINARY_API_KEY'),
    api_secret=os.getenv('CLOUDINARY_API_SECRET')
)

# Use CloudinaryField for image fields
from cloudinary.models import CloudinaryField

class Product(models.Model):
    image = CloudinaryField('image', folder='products/')
```

## Testing Procedures

### Sample Data Creation
```bash
python manage.py shell
```

```python
# Create sample service category
from myapp.models import ServiceCategory, Service

category = ServiceCategory.objects.create(
    title="Facial Treatments",
    description="Premium facial treatments for all skin types"
)

# Create sample service
service = Service.objects.create(
    name="Diamond Facial",
    description="Luxurious diamond-infused facial treatment",
    category=category,
    duration="90 minutes",
    price=4500.00,
    popular=True,
    process_steps=[
        "Deep cleansing with premium products",
        "Gentle exfoliation to remove dead skin",
        "Diamond microdermabrasion treatment"
    ],
    benefits=[
        "Removes dead skin cells",
        "Improves skin texture and tone",
        "Reduces fine lines and wrinkles"
    ]
)
```

## Deployment Checklist

### Production Settings
- [ ] Set `DEBUG = False`
- [ ] Configure proper `ALLOWED_HOSTS`
- [ ] Use PostgreSQL database
- [ ] Configure static/media file serving
- [ ] Set up HTTPS
- [ ] Configure email backend
- [ ] Set up logging
- [ ] Configure CORS properly
- [ ] Use environment variables for secrets

### Database Migration
```bash
python manage.py makemigrations
python manage.py migrate
python manage.py collectstatic
```

### Production Deployment (Example: Heroku)
```bash
# Install Heroku CLI and login
heroku create beauty-parlour-api
heroku addons:create heroku-postgresql
heroku config:set DEBUG=False
heroku config:set SECRET_KEY=your-production-secret-key
git push heroku main
heroku run python manage.py migrate
heroku run python manage.py createsuperuser
```

## Troubleshooting Guide

### Common Issues

1. **CORS Errors**
   - Check `CORS_ALLOWED_ORIGINS` in settings
   - Ensure frontend URL is included

2. **Image Upload Issues**
   - Check `MEDIA_ROOT` and `MEDIA_URL` settings
   - Verify file permissions
   - For production, ensure cloud storage is configured

3. **Database Connection Issues**
   - Verify `DATABASE_URL` in environment
   - Check database server status
   - Ensure proper credentials

4. **API Authentication Issues**
   - Check Django REST Framework settings
   - Verify authentication classes
   - Ensure proper token handling

### Performance Optimization

1. **Database Optimization**
   - Add proper indexes (already included in models)
   - Use `select_related()` and `prefetch_related()`
   - Implement database connection pooling

2. **API Optimization**
   - Implement pagination for list endpoints
   - Use API caching with Redis
   - Optimize serializers

3. **Image Optimization**
   - Use image compression
   - Implement lazy loading
   - Use appropriate image formats (WebP)

## Code Examples for Common Operations

### Custom API View Example
```python
from rest_framework import viewsets, filters
from django_filters.rest_framework import DjangoFilterBackend

class ServiceViewSet(viewsets.ModelViewSet):
    queryset = Service.objects.filter(is_active=True)
    serializer_class = ServiceSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchBackend]
    filterset_fields = ['category', 'popular']
    search_fields = ['name', 'description']
    
    def get_queryset(self):
        queryset = super().get_queryset()
        if self.action == 'list':
            queryset = queryset.select_related('category').prefetch_related('testimonials')
        return queryset
```

### Custom Management Command
```python
# management/commands/populate_sample_data.py
from django.core.management.base import BaseCommand
from myapp.models import Service, ServiceCategory

class Command(BaseCommand):
    help = 'Populate database with sample data'
    
    def handle(self, *args, **options):
        # Create sample data
        category, created = ServiceCategory.objects.get_or_create(
            title="Facial Treatments"
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS('Sample data created successfully')
            )
```

This documentation provides a comprehensive overview of the Django backend structure for the beauty parlour website. Update the models and API endpoints as needed based on your specific requirements.