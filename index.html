
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Mah Beauty - Best Beauty Parlour in Pokhara, Kaski | Premium Beauty Services & Products</title>
    <meta name="description" content="Mah Beauty - Leading beauty parlour in Pokhara, Kaski offering premium skincare, makeup, hair treatments, bridal services & authentic beauty products. Expert beauticians serving Pokhara, Lekhnath, Baglung, Parbat & surrounding areas." />
    <meta name="author" content="Mah Beauty" />

    <!-- SEO Keywords -->
    <meta name="keywords" content="beauty parlour <PERSON><PERSON><PERSON>, beauty salon <PERSON><PERSON>, makeup artist <PERSON><PERSON><PERSON>, bridal makeup Pokhara, skincare Pokhara, hair treatment <PERSON><PERSON>, beauty products <PERSON><PERSON><PERSON>, facial <PERSON>khar<PERSON>, beauty services <PERSON><PERSON><PERSON>, salon Pokhara, beautician <PERSON><PERSON>, cosmetics Pokhara, spa Pokhara, beauty parlour near me" />

    <!-- Location-based SEO -->
    <meta name="geo.region" content="NP-GA" />
    <meta name="geo.placename" content="Pokhara, Kaski, Nepal" />
    <meta name="geo.position" content="28.2096;83.9856" />
    <meta name="ICBM" content="28.2096, 83.9856" />

    <!-- Business Information -->
    <meta name="business.name" content="Mah Beauty" />
    <meta name="business.type" content="Beauty Parlour, Beauty Salon, Cosmetics Store" />
    <meta name="business.location" content="Pokhara, Kaski, Nepal" />
    <meta name="business.services" content="Beauty Services, Makeup, Skincare, Hair Treatment, Bridal Services, Beauty Products" />

    <!-- Additional SEO Meta Tags -->
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
    <meta name="googlebot" content="index, follow" />
    <meta name="language" content="English" />
    <meta name="revisit-after" content="7 days" />
    <meta name="rating" content="general" />
    <meta name="distribution" content="global" />
    <meta name="coverage" content="Worldwide" />
    <meta name="target" content="all" />
    <meta name="audience" content="all" />
    <meta name="theme-color" content="#8B1538" />

    <!-- Canonical URL -->
    <link rel="canonical" href="https://mah-beauty.com/" />

    <!-- Preload Critical Resources -->
    <link rel="preload" href="/src/main.tsx" as="script" />
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&display=swap" as="style" />

    <!-- DNS Prefetch for External Resources -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    <link rel="dns-prefetch" href="//fonts.gstatic.com" />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://mah-beauty.com/" />
    <meta property="og:title" content="Mah Beauty - Best Beauty Parlour in Pokhara, Kaski | Premium Beauty Services & Products" />
    <meta property="og:description" content="Leading beauty parlour in Pokhara, Kaski offering premium skincare, makeup, hair treatments, bridal services & authentic beauty products. Expert beauticians serving Pokhara, Lekhnath, Baglung, Parbat & surrounding areas." />
    <meta property="og:image" content="https://mah-beauty.com/opengraph-image-p98pqg.png" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:alt" content="Mah Beauty - Premium Beauty Parlour in Pokhara" />
    <meta property="og:site_name" content="Mah Beauty" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:locale:alternate" content="ne_NP" />

    <!-- Business Location for Social Media -->
    <meta property="business:contact_data:locality" content="Pokhara" />
    <meta property="business:contact_data:region" content="Kaski" />
    <meta property="business:contact_data:country_name" content="Nepal" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:url" content="https://mah-beauty.com/" />
    <meta name="twitter:title" content="Mah Beauty - Best Beauty Parlour in Pokhara, Kaski" />
    <meta name="twitter:description" content="Leading beauty parlour in Pokhara offering premium skincare, makeup, hair treatments & beauty products. Expert beauticians serving Kaski & surrounding areas." />
    <meta name="twitter:image" content="https://mah-beauty.com/opengraph-image-p98pqg.png" />
    <meta name="twitter:image:alt" content="Mah Beauty - Premium Beauty Parlour in Pokhara" />
    <meta name="twitter:site" content="@mah-beauty_in" />
    <meta name="twitter:creator" content="@mah-beauty_in" />

    <!-- Structured Data for Local Business SEO -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "BeautySalon",
      "name": "Mah Beauty",
      "description": "Leading beauty parlour in Pokhara, Kaski offering premium skincare, makeup, hair treatments, bridal services and authentic beauty products.",
      "url": "https://mah-beauty.com",
      "logo": "https://mah-beauty.com/logo.png",
      "image": "https://mah-beauty.com/opengraph-image-p98pqg.png",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Pokhara",
        "addressLocality": "Pokhara",
        "addressRegion": "Kaski",
        "addressCountry": "Nepal"
      },
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": "28.2096",
        "longitude": "83.9856"
      },
      "areaServed": [
        {
          "@type": "City",
          "name": "Pokhara"
        },
        {
          "@type": "City",
          "name": "Lekhnath"
        },
        {
          "@type": "City",
          "name": "Baglung"
        },
        {
          "@type": "City",
          "name": "Parbat"
        },
        {
          "@type": "State",
          "name": "Kaski"
        }
      ],
      "serviceType": [
        "Beauty Services",
        "Makeup Services",
        "Skincare Treatment",
        "Hair Treatment",
        "Bridal Makeup",
        "Facial Treatment",
        "Beauty Products",
        "Cosmetics"
      ],
      "priceRange": "$$",
      "openingHours": "Mo-Su 09:00-19:00",
      "sameAs": [
        "https://www.facebook.com/mah-beauty",
        "https://www.comstagram.com/mah-beauty_in",
        "https://twitter.com/mah-beauty_in"
      ]
    }
    </script>

    <!-- Additional Local Business Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Store",
      "name": "Mah Beauty Products Store",
      "description": "Authentic beauty products and cosmetics store in Pokhara, Kaski",
      "url": "https://mah-beauty.com/products",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Pokhara",
        "addressRegion": "Kaski",
        "addressCountry": "Nepal"
      },
      "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": "Beauty Products",
        "itemListElement": [
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Product",
              "name": "Skincare Products",
              "category": "Beauty & Personal Care"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Product",
              "name": "Makeup Products",
              "category": "Cosmetics"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Product",
              "name": "Hair Care Products",
              "category": "Hair Care"
            }
          }
        ]
      }
    }
    </script>

    <!-- GitHub Pages SPA redirect script -->
    <script type="text/javascript">
      // Single Page Apps for GitHub Pages
      // MIT License
      // https://github.com/rafgraph/spa-github-pages
      // This script checks to see if a redirect is present in the query string,
      // converts it back into the correct url and adds it to the
      // browser's history using window.history.replaceState(...),
      // which won't cause the browser to attempt to load the new url.
      // When the single page app is loaded further down in this file,
      // the correct url will be waiting in the browser's history for
      // the single page app to route accordingly.
      (function(l) {
        if (l.search[1] === '/' ) {
          var decoded = l.search.slice(1).split('&').map(function(s) {
            return s.replace(/~and~/g, '&')
          }).join('?');
          window.history.replaceState(null, null,
              l.pathname.slice(0, -1) + decoded + l.hash
          );
        }
      }(window.location))
    </script>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
