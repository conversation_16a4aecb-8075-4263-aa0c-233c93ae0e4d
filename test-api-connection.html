<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .product {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .product img {
            max-width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 4px;
            margin-right: 15px;
            float: left;
        }
        .product-info {
            overflow: hidden;
        }
        .product-name {
            font-weight: bold;
            color: #8B1538;
            margin-bottom: 5px;
        }
        .product-price {
            font-size: 18px;
            color: #8B1538;
            font-weight: bold;
        }
        .product-category {
            background-color: #8B1538;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            display: inline-block;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Beauty Website API Connection Test</h1>
        
        <div id="status" class="status loading">
            Testing API connection...
        </div>
        
        <div id="results"></div>
        
        <button onclick="testConnection()" style="margin-top: 20px; padding: 10px 20px; background-color: #8B1538; color: white; border: none; border-radius: 4px; cursor: pointer;">
            Test Again
        </button>
    </div>

    <script>
        async function testConnection() {
            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('results');
            
            statusDiv.className = 'status loading';
            statusDiv.textContent = 'Testing API connection...';
            resultsDiv.innerHTML = '';
            
            try {
                // Test products endpoint
                const response = await fetch('http://localhost:8000/api/products/products/', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const products = await response.json();
                
                statusDiv.className = 'status success';
                statusDiv.innerHTML = `
                    ✅ API Connection Successful!<br>
                    📡 Backend: http://localhost:8000<br>
                    🎯 Frontend: http://localhost:8080<br>
                    📦 Products found: ${products.length}
                `;
                
                // Display products
                if (products.length > 0) {
                    resultsDiv.innerHTML = '<h3>Products from Backend:</h3>';
                    products.forEach(product => {
                        const productDiv = document.createElement('div');
                        productDiv.className = 'product';
                        productDiv.innerHTML = `
                            <img src="${product.image || 'https://via.placeholder.com/100x100/8B1538/FFFFFF?text=No+Image'}" 
                                 alt="${product.name}" 
                                 onerror="this.src='https://via.placeholder.com/100x100/8B1538/FFFFFF?text=No+Image'">
                            <div class="product-info">
                                <div class="product-name">${product.name}</div>
                                <div>Rs. ${product.price}</div>
                                <div class="product-price">Rating: ${product.rating}/5</div>
                                <div class="product-category">${product.category_name}</div>
                                <div style="margin-top: 5px; font-size: 12px; color: #666;">
                                    Stock: ${product.in_stock ? '✅ Available' : '❌ Out of Stock'}
                                </div>
                            </div>
                            <div style="clear: both;"></div>
                        `;
                        resultsDiv.appendChild(productDiv);
                    });
                } else {
                    resultsDiv.innerHTML = '<p>No products found in the database.</p>';
                }
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `
                    ❌ API Connection Failed!<br>
                    Error: ${error.message}<br>
                    <br>
                    <strong>Troubleshooting:</strong><br>
                    1. Make sure Django backend is running on http://localhost:8000<br>
                    2. Check CORS settings in Django<br>
                    3. Verify the API endpoint exists<br>
                    4. Check browser console for more details
                `;
                
                resultsDiv.innerHTML = `
                    <h3>Error Details:</h3>
                    <pre style="background: #f8f8f8; padding: 10px; border-radius: 4px; overflow-x: auto;">${error.stack || error.message}</pre>
                `;
            }
        }
        
        // Test connection on page load
        window.addEventListener('load', testConnection);
    </script>
</body>
</html>
