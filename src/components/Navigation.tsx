
import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { Menu, X, Phone, ShoppingCart, User, LogOut, Settings, Package } from 'lucide-react';
import { useOrganizationInfo } from '@/hooks/useOrganizationInfo';
import { useAuth } from '@/contexts/AuthContext';
import { useCart } from '@/contexts/CartContext';

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { orgInfo } = useOrganizationInfo();
  const { user, isAuthenticated, logout } = useAuth();
  const { getTotalItems } = useCart();

  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'Products', path: '/products' },
    { name: 'About', path: '/about' },
    { name: 'Contact', path: '/contact' },
  ];

  const handleLogout = async () => {
    await logout();
    navigate('/');
  };

  const isActive = (path: string) => location.pathname === path;

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 glass-effect bg-gold-50 border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            {orgInfo?.logo ? (
              <img
                src={orgInfo.logo}
                alt={orgInfo.name}
                className="w-8 h-8 rounded-full object-cover"
              />
            ) : (
              <div className="w-8 h-8 luxury-gradient rounded-full flex items-center justify-center">
                <span className="text-white font-playfair font-bold text-lg">
                  {orgInfo?.name?.charAt(0) || 'B'}
                </span>
              </div>
            )}
            <span className="font-playfair font-bold text-xl text-burgundy-800">
              {orgInfo?.name || 'Beauty Parlour'}
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            {navItems.map((item) => (
              <Link
                key={item.name}
                to={item.path}
                className={`text-sm font-medium transition-colors duration-200 ${
                  isActive(item.path)
                    ? 'text-burgundy-800 border-b-2 border-gold-400'
                    : 'text-burgundy-600 hover:text-burgundy-800'
                }`}
              >
                {item.name}
              </Link>
            ))}

            {/* Cart Icon */}
            {isAuthenticated && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/cart')}
                className="relative text-burgundy-600 hover:text-burgundy-800"
              >
                <ShoppingCart className="w-5 h-5" />
                {getTotalItems() > 0 && (
                  <Badge className="absolute -top-2 -right-2 bg-burgundy-800 text-white text-xs min-w-[1.25rem] h-5 flex items-center justify-center rounded-full">
                    {getTotalItems()}
                  </Badge>
                )}
              </Button>
            )}

            {/* User Menu or Login */}
            {isAuthenticated ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="text-burgundy-600 hover:text-burgundy-800">
                    <User className="w-4 h-4 mr-2" />
                    {user?.first_name || 'Account'}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem onClick={() => navigate('/profile')}>
                    <Settings className="w-4 h-4 mr-2" />
                    Profile
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate('/orders')}>
                    <Package className="w-4 h-4 mr-2" />
                    My Orders
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogOut className="w-4 h-4 mr-2" />
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate('/login')}
                  className="text-burgundy-600 hover:text-burgundy-800"
                >
                  Login
                </Button>
                <Button
                  className="luxury-gradient text-white hover:opacity-90"
                  size="sm"
                  onClick={() => navigate('/register')}
                >
                  Sign Up
                </Button>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(!isOpen)}
              className="text-burgundy-800"
            >
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-gold-50 backdrop-blur-sm rounded-lg mt-2 border">
              {navItems.map((item) => (
                <Link
                  key={item.name}
                  to={item.path}
                  className={`block px-3 py-2 text-base font-medium rounded-md transition-colors ${
                    isActive(item.path)
                      ? 'text-burgundy-800 bg-gold-100'
                      : 'text-burgundy-600 hover:text-burgundy-800 hover:bg-cream-200'
                  }`}
                  onClick={() => setIsOpen(false)}
                >
                  {item.name}
                </Link>
              ))}

              {/* Mobile Cart and Auth */}
              <div className="px-3 py-2 space-y-2">
                {isAuthenticated && (
                  <Button
                    variant="outline"
                    className="w-full border-burgundy-300 text-burgundy-700"
                    onClick={() => {
                      setIsOpen(false);
                      navigate('/cart');
                    }}
                  >
                    <ShoppingCart className="w-4 h-4 mr-2" />
                    Cart ({getTotalItems()})
                  </Button>
                )}

                {isAuthenticated ? (
                  <div className="space-y-2">
                    <Button
                      variant="outline"
                      className="w-full border-burgundy-300 text-burgundy-700"
                      onClick={() => {
                        setIsOpen(false);
                        navigate('/profile');
                      }}
                    >
                      <User className="w-4 h-4 mr-2" />
                      Profile
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full border-burgundy-300 text-burgundy-700"
                      onClick={() => {
                        setIsOpen(false);
                        navigate('/orders');
                      }}
                    >
                      <Package className="w-4 h-4 mr-2" />
                      My Orders
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full border-burgundy-300 text-burgundy-700"
                      onClick={() => {
                        setIsOpen(false);
                        handleLogout();
                      }}
                    >
                      <LogOut className="w-4 h-4 mr-2" />
                      Logout
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Button
                      variant="outline"
                      className="w-full border-burgundy-300 text-burgundy-700"
                      onClick={() => {
                        setIsOpen(false);
                        navigate('/login');
                      }}
                    >
                      Login
                    </Button>
                    <Button
                      className="w-full luxury-gradient text-white"
                      onClick={() => {
                        setIsOpen(false);
                        navigate('/register');
                      }}
                    >
                      Sign Up
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;
