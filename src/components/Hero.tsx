import { useState, useEffect } from 'react';
import { Star, ArrowDown, Sparkles, Phone } from 'lucide-react';

// Mock navigation function for demonstration
const useNavigate = () => {
  return (path) => console.log(`Navigate to: ${path}`);
};

// Mock API service for demonstration
const getHeroSlides = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  return null; // This will trigger fallback data
};

// Mock Button component
const Button = ({ children, className, onClick, size, variant, ...props }) => (
  <button
    className={`inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background ${className}`}
    onClick={onClick}
    {...props}
  >
    {children}
  </button>
);

interface HeroImage {
  id: number;
  image_url: string;
  image: string;
}

interface HeroSlide {
  id: number;
  title: string;
  description: string;
  client: number;
  service: number;
  experience: number;
  created_at: string;
  updated_at: string;
  images: HeroImage[];
}

const Hero = () => {
  const navigate = useNavigate();
  const [imageLoaded, setImageLoaded] = useState(false);
  const [currentImage, setCurrentImage] = useState(0);
  const [heroData, setHeroData] = useState<HeroSlide | null>(null);
  const [loading, setLoading] = useState(true);

  // Fallback data for when API is not available
  const fallbackData: HeroSlide = {
    id: 1,
    title: "Where Beauty Meets Luxury",
    description: "Indulge in premium beauty treatments crafted with precision and care. Experience the epitome of elegance at our exclusive beauty parlour.",
    client: 500,
    service: 50,
    experience: 5,
    created_at: "",
    updated_at: "",
    images: [
      {
        id: 1,
        image_url: 'https://images.unsplash.com/photo-1520472354-b33ff0c44a43?q=80&w=2126&auto=format&fit=crop',
        image: 'https://images.unsplash.com/photo-1520472354-b33ff0c44a43?q=80&w=2126&auto=format&fit=crop'
      },
      {
        id: 2,
        image_url: 'https://images.unsplash.com/photo-1522337320788-8b13dee7a37e?q=80&w=2069&auto=format&fit=crop',
        image: 'https://images.unsplash.com/photo-1522337320788-8b13dee7a37e?q=80&w=2069&auto=format&fit=crop'
      },
      {
        id: 3,
        image_url: 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?q=80&w=2069&auto=format&fit=crop',
        image: 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?q=80&w=2069&auto=format&fit=crop'
      }
    ]
  };

  useEffect(() => {
    const fetchHeroData = async () => {
      try {
        setLoading(true);
        const response = await getHeroSlides();

        if (response && response.success && response.data && response.data.length > 0) {
          setHeroData(response.data[0]);
        } else {
          setHeroData(fallbackData);
        }
      } catch (error) {
        setHeroData(fallbackData);
      } finally {
        setLoading(false);
      }
    };

    fetchHeroData();
  }, []);

  // Auto-rotate images every 5 seconds
  useEffect(() => {
    if (!heroData) return;

    const interval = setInterval(() => {
      setCurrentImage((prev) => (prev + 1) % heroData.images.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [heroData]);

  const currentHeroData = heroData || fallbackData;
  const heroImages = currentHeroData.images.map(img => img.image_url || img.image);

  if (loading) {
    return (
      <section className="min-h-screen relative overflow-hidden bg-gradient-to-br from-amber-50 via-orange-50 to-pink-50 flex items-center justify-center">
        <div className="text-center space-y-6">
          <div className="relative">
            <div className="w-16 h-16 border-4 border-pink-200 border-t-pink-500 rounded-full animate-spin mx-auto"></div>
            <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-amber-400 rounded-full animate-spin mx-auto" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
          </div>
          <div className="text-pink-700 font-light text-lg tracking-wide">Preparing your luxury experience...</div>
        </div>
      </section>
    );
  }

  return (
    <>
      <section className="min-h-screen relative overflow-hidden">
        {/* Sophisticated background with Ken Burns effect */}
        <div className="absolute inset-0">
          <div className="relative w-full h-full">
            {heroImages.map((image, index) => (
              <div
                key={index}
                className={`absolute inset-0 transition-all duration-[2000ms] ease-in-out ${index === currentImage
                  ? 'opacity-100 scale-110'
                  : 'opacity-0 scale-105'
                  }`}
              >
                <img
                  src={image}
                  alt={`Hero ${index + 1}`}
                  className="w-full h-full object-cover ken-burns-effect"
                  onLoad={() => setImageLoaded(true)}
                />
              </div>
            ))}
          </div>

          {/* Elegant multi-layer overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/30 to-rose-900/60"></div>
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-black/20 to-black/60"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-rose-900/40 via-transparent to-transparent"></div>
        </div>

        {/* Main Content Container */}
        <div className="relative z-10 min-h-screen flex items-center">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">

              {/* Left Column - Main Content */}
              <div className="space-y-8 lg:space-y-12 text-center lg:text-left">

                {/* Luxury Badge */}
                <div className="inline-flex items-center space-x-3 bg-white/10 backdrop-blur-xl px-6 py-3 rounded-full border border-amber-400/20 shadow-2xl">
                  <div className="flex items-center space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-amber-400 text-amber-400 animate-pulse" style={{ animationDelay: `${i * 0.2}s` }} />
                    ))}
                  </div>
                  <span className="text-white font-light text-sm tracking-widest uppercase">Luxury Experience</span>
                </div>

                {/* Main Headline */}
                <div className="space-y-4">
                  <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-light leading-[1.1] text-white">
                    <span className="block font-extralight tracking-tight">
                      {currentHeroData.title.split(' ').slice(0, 2).join(' ')}
                    </span>
                    <span className="block text-amber-400 font-normal bg-gradient-to-r from-amber-300 to-amber-500 bg-clip-text text-transparent">
                      {currentHeroData.title.split(' ').slice(2).join(' ')}
                    </span>
                  </h1>

                  {/* Elegant divider */}
                  <div className="flex justify-center lg:justify-start">
                    <div className="w-24 h-0.5 bg-gradient-to-r from-transparent via-amber-400 to-transparent"></div>
                  </div>
                </div>

                {/* Description */}
                <p className="text-lg lg:text-xl text-orange-100 leading-relaxed max-w-2xl mx-auto lg:mx-0 font-light tracking-wide">
                  {currentHeroData.description}
                </p>

                {/* CTA Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                  <Button
                    size="lg"
                    className="group relative bg-gradient-to-r from-rose-500 to-rose-600 hover:from-rose-600 hover:to-rose-700 text-white px-8 py-6 text-lg font-light tracking-wide rounded-full shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105 border-0 overflow-hidden"
                    onClick={() => navigate('/products')}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-amber-400/0 via-amber-400/20 to-amber-400/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>
                    <Sparkles className="w-5 h-5 mr-3 group-hover:rotate-180 transition-transform duration-500" />
                    Explore Treatments
                  </Button>

                  <Button
                    variant="outline"
                    size="lg"
                    className="group bg-white/10 backdrop-blur-xl border-2 border-white/30 text-white hover:bg-white hover:text-rose-800 px-8 py-6 text-lg font-light tracking-wide rounded-full shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105"
                    onClick={() => navigate('/contact')}
                  >
                    <Phone className="w-5 h-5 mr-3 group-hover:scale-110 transition-transform duration-300" />
                    Book Consultation
                  </Button>
                </div>
              </div>

              {/* Right Column - Stats */}
              <div className="space-y-8">
                <div className="grid gap-6">
                  {[
                    { value: currentHeroData.client, label: 'Satisfied Clients', suffix: '+' },
                    { value: currentHeroData.service, label: 'Premium Services', suffix: '+' },
                    { value: currentHeroData.experience, label: 'Years of Excellence', suffix: '+' }
                  ].map((stat, index) => (
                    <div key={index} className="group relative">
                      <div className="bg-white/5 backdrop-blur-xl rounded-2xl p-8 border border-white/10 shadow-2xl hover:bg-white/10 transition-all duration-500 hover:scale-105">
                        <div className="absolute inset-0 bg-gradient-to-r from-rose-500/10 to-amber-400/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        <div className="relative z-10">
                          <div className="text-5xl lg:text-6xl font-light text-amber-400 mb-2 tabular-nums">
                            {stat.value.toLocaleString()}{stat.suffix}
                          </div>
                          <div className="text-orange-200 text-sm uppercase tracking-widest font-light">
                            {stat.label}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Image Navigation */}
                <div className="flex justify-center lg:justify-start space-x-4 mt-8">
                  {heroImages.map((_, index) => (
                    <button
                      key={index}
                      className={`relative w-12 h-2 rounded-full transition-all duration-500 overflow-hidden ${index === currentImage
                        ? 'bg-amber-400 shadow-lg shadow-amber-400/50'
                        : 'bg-white/20 hover:bg-white/40'
                        }`}
                      onClick={() => setCurrentImage(index)}
                    >
                      {index === currentImage && (
                        <div className="absolute inset-0 bg-gradient-to-r from-amber-300 to-amber-500 animate-pulse"></div>
                      )}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
          <div className="flex flex-col items-center space-y-2 animate-bounce">
            <div className="w-6 h-10 border-2 border-white/40 rounded-full flex justify-center">
              <div className="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
            </div>
            <div className="text-white/60 text-xs uppercase tracking-widest">Scroll</div>
          </div>
        </div>

        {/* Floating elements for added sophistication */}
        <div className="absolute top-1/4 right-8 w-2 h-2 bg-amber-400/30 rounded-full animate-pulse"></div>
        <div className="absolute top-1/3 left-8 w-1 h-1 bg-rose-400/40 rounded-full animate-ping"></div>
        <div className="absolute bottom-1/4 right-16 w-3 h-3 border border-orange-300/20 rounded-full animate-pulse"></div>
      </section>

      <style>{`
        @keyframes ken-burns {
          0% { transform: scale(1) translate(0, 0); }
          25% { transform: scale(1.08) translate(-2%, -1%); }
          50% { transform: scale(1.12) translate(1%, -2%); }
          75% { transform: scale(1.1) translate(-1%, 1%); }
          100% { transform: scale(1.15) translate(1%, 0%); }
        }
        
        .ken-burns-effect {
          animation: ken-burns 20s ease-in-out infinite;
        }

        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          33% { transform: translateY(-10px) rotate(1deg); }
          66% { transform: translateY(5px) rotate(-1deg); }
        }
        
        .floating {
          animation: float 6s ease-in-out infinite;
        }

        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }

        .shimmer::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
          );
          animation: shimmer 2s infinite;
        }

        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
          width: 8px;
        }
        
        ::-webkit-scrollbar-track {
          background: rgba(0,0,0,0.1);
        }
        
        ::-webkit-scrollbar-thumb {
          background: linear-gradient(to bottom, #f59e0b, #d97706);
          border-radius: 4px;
        }

        /* Smooth text rendering */
        * {
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }
      `}</style>
    </>
  );
};

export default Hero;