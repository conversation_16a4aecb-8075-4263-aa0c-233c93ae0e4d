// E-commerce type definitions for the beauty website

export interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  profile_image?: string;
  date_joined: string;
  is_active: boolean;
}

export interface ProductCategory {
  id: number;
  name: string;
  description?: string;
  image?: string;
  is_active: boolean;
  ean_category_code?: string;
}

export interface Product {
  id: number;
  name: string;
  description?: string;
  price: string;
  category?: ProductCategory; // For single product API response
  category_name?: string; // For products list API response
  rating: number;
  average_rating?: number;
  review_count?: number;
  in_stock: boolean;
  ingredients?: Array<{
    name: string;
    quantity: string;
  }> | null;
  usage_instructions?: string | null;
  benefits?: string[] | null;
  image?: string | null;
  image_url?: string;
  created_at: string;
  updated_at?: string;
  is_active: boolean;
  is_on_offer?: boolean;
  offer_price?: string | null;
  offer_start_date?: string | null;
  offer_end_date?: string | null;
  offer_is_active?: boolean;
  has_offer?: boolean;
  discounted_price?: number;
  current_price?: number;
  discount_percentage?: number;
  savings_amount?: number;
  is_offer_valid?: boolean;
  variations?: any[];
  reviews?: any[];
  ean_13_code?: string;
}

export interface Variation {
  id: number;
  variation_category: string;
  variation_value: string;
  is_active: boolean;
  created_date: string;
}

export interface Review {
  id: number;
  product: number;
  user: User;
  rating: number;
  comment: string;
  status: boolean;
  created_at: string;
  updated_at: string;
}

export interface Cart {
  id: number;
  cart_id: string;
  date_added: string;
  items_count: number;
  total_amount: number;
  total_quantity: number;
}

export interface CartItem {
  id: number;
  user?: User;
  product: Product;
  variation: Variation[];
  cart: Cart;
  quantity: number;
  is_active: boolean;
  sub_total: number;
}

export interface PaymentMethod {
  id: number;
  name: string;
  image?: string;
  is_active: boolean;
}

export interface Payment {
  id: number;
  user?: User;
  payment_id: string;
  payment_method?: PaymentMethod;
}

export interface Order {
  id: number;
  user?: User;
  payment?: Payment;
  order_number: string;
  first_name: string;
  last_name: string;
  phone: string;
  email: string;
  state: string;
  area: string;
  address: string;
  status: 'Processing' | 'Packed' | 'Shipped' | 'Delivered' | 'Cancel';
  ip?: string;
  grand_total: number;
  tax: number;
  is_ordered: boolean;
  order_note?: string;
  created_at: string;
  updated_at: string;
}

export interface OrderProduct {
  id: number;
  user: User;
  payment?: Payment;
  order: Order;
  product: Product;
  variation: Variation[];
  quantity: number;
  product_price: number;
  ordered: boolean;
  created_at: string;
  updated_at: string;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  status?: string;
  message?: string;
}

export interface AuthResponse {
  access: string;
  refresh: string;
  user?: User;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone?: string;
}

export interface CheckoutData {
  first_name: string;
  last_name: string;
  phone: string;
  email: string;
  state: string;
  area: string;
  address: string;
  order_note?: string;
  payment_method?: number;
}

// Cart context types
export interface CartContextType {
  cart: Cart | null;
  cartItems: CartItem[];
  loading: boolean;
  addToCart: (productId: number, quantity: number, variationIds?: number[]) => Promise<void>;
  updateCartItem: (itemId: number, quantity: number) => Promise<void>;
  removeFromCart: (itemId: number) => Promise<void>;
  clearCart: () => void;
  refreshCart: () => Promise<void>;
  getTotalItems: () => number;
  getTotalAmount: () => number;
}

// Auth context types
export interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  login: (credentials: LoginCredentials) => Promise<boolean>;
  loginWithGoogle: (credential: string) => Promise<boolean>;
  register: (userData: RegisterData) => Promise<boolean>;
  logout: () => void;
  updateProfile: (userData: Partial<User>) => Promise<boolean>;
  changePassword: (passwordData: { current_password: string; new_password: string; re_new_password: string; }) => Promise<boolean>;
  requestPasswordReset: (email: string) => Promise<boolean>;
  refreshUser: () => Promise<void>;
}
