import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { 
  getUserCart, 
  getCartItems, 
  addToCart as addToCartAPI, 
  updateCartItem as updateCartItemAPI, 
  remove<PERSON>romCart as removeFrom<PERSON>art<PERSON>I,
  createCart
} from '@/api/apiService';
import { Cart, CartItem, CartContextType } from '@/types/ecommerce';
import { useAuth } from './AuthContext';
import { toast } from 'react-toastify';

const CartContext = createContext<CartContextType | undefined>(undefined);

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

interface CartProviderProps {
  children: ReactNode;
}

export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const [cart, setCart] = useState<Cart | null>(null);
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(false);
  const { isAuthenticated, user } = useAuth();

  // Initialize cart when user authentication changes
  useEffect(() => {
    if (isAuthenticated && user) {
      refreshCart();
    } else {
      // Clear cart when user logs out
      setCart(null);
      setCartItems([]);
    }
  }, [isAuthenticated, user]);

  const refreshCart = async () => {
    if (!isAuthenticated) return;

    try {
      setLoading(true);
      
      // Get user's cart
      const cartResponse = await getUserCart();
      if (cartResponse.success && cartResponse.data) {
        const userCarts = Array.isArray(cartResponse.data) ? cartResponse.data : [cartResponse.data];
        if (userCarts.length > 0) {
          setCart(userCarts[0]); // Use the first cart
        }
      }

      // Get cart items
      const itemsResponse = await getCartItems();
      if (itemsResponse.success && itemsResponse.data) {
        const items = Array.isArray(itemsResponse.data) ? itemsResponse.data : [];
        // Filter out items with missing product data
        const validItems = items.filter(item => item && item.product);
        if (validItems.length !== items.length) {
          console.warn('Some cart items are missing product data:', items.filter(item => !item || !item.product));
        }
        setCartItems(validItems);
      }
    } catch (error) {
      console.error('Error refreshing cart:', error);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = async (productId: number, quantity: number, variationIds?: number[]) => {
    if (!isAuthenticated) {
      toast.error('Please login to add items to cart');
      return;
    }

    try {
      setLoading(true);
      
      // Don't create cart explicitly - backend will handle this automatically
      // when adding the first item

      const response = await addToCartAPI({
        product_id: productId,
        quantity,
        variation_ids: variationIds
      });

      if (response.status === "200") {
        toast.success('Item added to cart!');
        await refreshCart(); // Refresh cart to get updated data
      } else {
        toast.error(response.message || 'Failed to add item to cart');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to add item to cart');
    } finally {
      setLoading(false);
    }
  };

  const updateCartItem = async (itemId: number, quantity: number) => {
    if (!isAuthenticated) {
      toast.error('Please login to update cart');
      return;
    }

    try {
      setLoading(true);
      const response = await updateCartItemAPI(itemId, { quantity });

      if (response.status === "200") {
        toast.success('Cart updated!');
        await refreshCart(); // Refresh cart to get updated data
      } else {
        toast.error(response.message || 'Failed to update cart item');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to update cart item');
    } finally {
      setLoading(false);
    }
  };

  const removeFromCart = async (itemId: number) => {
    if (!isAuthenticated) {
      toast.error('Please login to remove items from cart');
      return;
    }

    try {
      setLoading(true);
      const response = await removeFromCartAPI(itemId);

      if (response.status === "200") {
        await refreshCart(); // Refresh cart to get updated data
      } else {
        toast.error(response.message || 'Failed to remove item from cart');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to remove item from cart');
    } finally {
      setLoading(false);
    }
  };

  const clearCart = () => {
    setCart(null);
    setCartItems([]);
  };

  const getTotalItems = (): number => {
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  };

  const getTotalAmount = (): number => {
    return cartItems.reduce((total, item) => {
      const price = parseFloat(item.product.price) || 0;
      return total + (price * item.quantity);
    }, 0);
  };

  const value: CartContextType = {
    cart,
    cartItems,
    loading,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    refreshCart,
    getTotalItems,
    getTotalAmount,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};

export default CartContext;
