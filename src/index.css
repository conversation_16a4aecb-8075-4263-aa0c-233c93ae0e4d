
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom beauty parlour design system */

@layer base {
  :root {
    --background: 43 21% 95%; /* cream-300 */
    --foreground: 339 66% 15%; /* burgundy-900 */

    --card: 0 0% 100%;
    --card-foreground: 339 66% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 339 66% 15%;

    --primary: 339 71% 27%; /* burgundy-800 */
    --primary-foreground: 43 21% 95%;

    --secondary: 43 21% 91%; /* cream-400 */
    --secondary-foreground: 339 66% 15%;

    --muted: 43 21% 91%;
    --muted-foreground: 43 8% 46%;

    --accent: 45 93% 47%; /* gold-400 */
    --accent-foreground: 339 66% 15%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 43 21% 87%;
    --input: 43 21% 87%;
    --ring: 339 71% 27%;

    --radius: 0.75rem;
  }

  .dark {
    --background: 339 66% 15%;
    --foreground: 43 21% 95%;

    --card: 339 59% 18%;
    --card-foreground: 43 21% 95%;

    --popover: 339 59% 18%;
    --popover-foreground: 43 21% 95%;

    --primary: 43 21% 95%;
    --primary-foreground: 339 71% 27%;

    --secondary: 339 59% 18%;
    --secondary-foreground: 43 21% 95%;

    --muted: 339 59% 18%;
    --muted-foreground: 43 8% 65%;

    --accent: 45 93% 47%;
    --accent-foreground: 339 66% 15%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 339 59% 18%;
    --input: 339 59% 18%;
    --ring: 45 93% 47%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-playfair;
  }
}

@layer components {
  .luxury-gradient {
    @apply bg-gradient-to-br from-burgundy-800 via-burgundy-700 to-burgundy-900;
  }

  .gold-gradient {
    @apply bg-gradient-to-r from-gold-400 to-gold-500;
  }

  .cream-gradient {
    @apply bg-gradient-to-br from-cream-200 to-cream-300;
  }

  .hover-lift {
    @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
  }

  .glass-effect {
    @apply backdrop-blur-sm bg-white/80 border border-white/20;
  }
}
