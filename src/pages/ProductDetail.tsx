import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Star,
  CheckCircle,
  ArrowLeft,
  Phone,
  ShoppingCart,
  Package,
  Leaf,
  Info,
  Plus,
  Minus
} from 'lucide-react';
import { getProductByIdFromBackend } from '@/api/apiService';
import { Product } from '@/types/ecommerce';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';

const ProductDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [quantity, setQuantity] = useState(1);
  const { addToCart, loading: cartLoading } = useCart();
  const { isAuthenticated } = useAuth();

  // Fetch product details
  useEffect(() => {
    const fetchProduct = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const response = await getProductByIdFromBackend(parseInt(id));
        if (response && response.success) {
          setProduct(response.data);
        }
      } catch (error) {
        console.error('Error fetching product:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [id]);

  const handleContactForPurchase = () => {
    navigate('/contact', {
      state: {
        selectedProduct: product?.name,
        productId: product?.id
      }
    });
  };

  const handleAddToCart = async () => {
    if (!product) return;

    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    await addToCart(product.id, quantity);
  };

  const incrementQuantity = () => {
    setQuantity(prev => prev + 1);
  };

  const decrementQuantity = () => {
    setQuantity(prev => Math.max(1, prev - 1));
  };

  // Helper function to get category name from either structure
  const getCategoryName = () => {
    if (!product) return '';

    // Handle both API response structures
    if (typeof product.category === 'object' && product.category?.name) {
      return product.category.name;
    }

    return product.category_name || '';
  };

  // Helper function to get product image with fallbacks
  const getProductImage = () => {
    if (product?.image || product?.image_url) {
      return product.image || product.image_url;
    }

    // Fallback images based on category
    const categoryImages: { [key: string]: string } = {
      'Skincare': 'https://images.unsplash.com/photo-1556228720-195a672e8a03?w=600&h=600&fit=crop',
      'Makeup': 'https://images.unsplash.com/photo-1586495777744-4413f21062fa?w=600&h=600&fit=crop',
      'Hair Care': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600&h=600&fit=crop',
    };

    return categoryImages[getCategoryName()] || 'https://via.placeholder.com/600x600/8B1538/FFFFFF?text=Beauty+Product';
  };

  if (loading) {
    return (
      <div className="pt-20 min-h-screen bg-cream-200 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-burgundy-600 mx-auto mb-4"></div>
          <p className="text-burgundy-600">Loading product details...</p>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="pt-20 min-h-screen bg-cream-200 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-burgundy-800 mb-4">Product Not Found</h2>
          <Button onClick={() => navigate('/products')}>
            Back to Products
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-20 min-h-screen bg-cream-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        
        {/* Back Navigation */}
        <Button 
          variant="ghost" 
          className="mb-8 text-burgundy-600 hover:text-burgundy-800"
          onClick={() => navigate('/products')}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Products
        </Button>

        {/* Product Header */}
        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          <div>
            <div className="relative rounded-2xl overflow-hidden">
              <img
                src={getProductImage()}
                alt={product.name}
                className="w-full h-96 object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = 'https://via.placeholder.com/600x600/8B1538/FFFFFF?text=Beauty+Product';
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-burgundy-900/50 to-transparent"></div>
              
              {(product.in_stock || product.inStock) ? (
                <Badge className="absolute top-4 left-4 bg-green-500 text-white">
                  In Stock
                </Badge>
              ) : (
                <Badge className="absolute top-4 left-4 bg-red-500 text-white">
                  Out of Stock
                </Badge>
              )}

              <div className="absolute bottom-4 left-4 text-white">
                <div className="text-3xl font-bold font-playfair">Rs. {product.price}</div>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div>
              <Badge className="bg-burgundy-100 text-burgundy-800 mb-4">
                {getCategoryName()}
              </Badge>
              <h1 className="text-4xl font-bold font-playfair text-burgundy-800 mb-4">
                {product.name}
              </h1>
              <p className="text-xl text-burgundy-600 leading-relaxed">
                {product.description}
              </p>
            </div>

            <div className="grid grid-cols-2 gap-6">
              <div className="flex items-center text-burgundy-600">
                <Package className="w-5 h-5 mr-3" />
                <div>
                  <div className="font-semibold">Category</div>
                  <div>{getCategoryName()}</div>
                </div>
              </div>

              <div className="flex items-center text-burgundy-600">
                <Star className="w-5 h-5 mr-3 fill-gold-400 text-gold-400" />
                <div>
                  <div className="font-semibold">Rating</div>
                  <div>{product.rating}/5</div>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              {/* Price Display */}
              <div className="flex items-center space-x-2">
                <div className="text-3xl font-bold font-playfair text-burgundy-800">
                  Rs. {product.is_on_offer && product.offer_price ? product.offer_price : product.price}
                </div>
                {product.is_on_offer && product.offer_price && (
                  <div className="text-xl text-gray-500 line-through">
                    Rs. {product.price}
                  </div>
                )}
              </div>

              {/* Quantity Selector */}
              {product.in_stock && (
                <div className="flex items-center space-x-4">
                  <span className="text-burgundy-700 font-medium">Quantity:</span>
                  <div className="flex items-center space-x-3">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={decrementQuantity}
                      disabled={quantity <= 1}
                      className="w-10 h-10 p-0"
                    >
                      <Minus className="w-4 h-4" />
                    </Button>
                    <span className="font-medium text-burgundy-800 min-w-[2rem] text-center text-lg">
                      {quantity}
                    </span>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={incrementQuantity}
                      className="w-10 h-10 p-0"
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}

              {/* Add to Cart Button */}
              <Button
                size="lg"
                className="w-full luxury-gradient text-white hover:opacity-90"
                onClick={handleAddToCart}
                disabled={!product.in_stock || cartLoading}
              >
                <ShoppingCart className="w-5 h-5 mr-2" />
                {!product.in_stock ? 'Out of Stock' : cartLoading ? 'Adding...' : 'Add to Cart'}
              </Button>

              {/* Contact Button */}
              <Button
                variant="outline"
                size="lg"
                className="w-full border-burgundy-300 text-burgundy-700 hover:bg-burgundy-50"
                onClick={handleContactForPurchase}
              >
                <Phone className="w-5 h-5 mr-2" />
                Contact for Questions
              </Button>
            </div>
          </div>
        </div>

        {/* Ingredients Section */}
        {product.ingredients && product.ingredients.length > 0 && (
          <Card className="mb-16 bg-white/90 backdrop-blur-sm border-cream-300">
            <CardContent className="p-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center mr-4">
                  <Leaf className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold font-playfair text-burgundy-800">
                  Key Ingredients
                </h3>
              </div>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {product.ingredients.map((ingredient, index) => (
                  <div key={index} className="flex items-center p-3 bg-cream-100 rounded-lg">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3 shrink-0" />
                    <div>
                      <span className="font-semibold text-burgundy-800">{ingredient.name}</span>
                      {ingredient.quantity && (
                        <span className="text-burgundy-600 ml-2">({ingredient.quantity})</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Benefits & Usage */}
        <div className="grid lg:grid-cols-2 gap-8 mb-16">
          {/* Benefits */}
          {product.benefits && product.benefits.length > 0 && (
            <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center mr-4">
                    <CheckCircle className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold font-playfair text-burgundy-800">
                    Key Benefits
                  </h3>
                </div>
                <ul className="space-y-3">
                  {product.benefits.map((benefit, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 shrink-0" />
                      <span className="text-burgundy-600">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Usage Instructions */}
          {(product.usage_instructions || product.usage) && (
            <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center mr-4">
                    <Info className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold font-playfair text-burgundy-800">
                    Usage Instructions
                  </h3>
                </div>
                <p className="text-burgundy-600 leading-relaxed">
                  {product.usage_instructions || product.usage}
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* CTA Section */}
        <div className="text-center luxury-gradient rounded-2xl p-12 text-white">
          <h2 className="text-3xl font-bold font-playfair mb-4">
            Ready to Purchase {product.name}?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Contact us today for pricing, availability, and purchase details
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="bg-white text-burgundy-800 hover:bg-cream-100 px-8 py-4 text-lg"
              onClick={handleContactForPurchase}
              disabled={!(product.in_stock || product.inStock)}
            >
              <ShoppingCart className="w-5 h-5 mr-2" />
              Contact for Purchase
            </Button>
            <Button 
              variant="outline" 
              size="lg" 
              className="border-white text-burgundy-800 hover:text-white hover:bg-white/10 px-8 py-4 text-lg"
              onClick={() => navigate('/products')}
            >
              View All Products
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
