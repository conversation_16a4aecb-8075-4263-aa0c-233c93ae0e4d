import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ShoppingCart, Plus, Minus, Trash2, ArrowLeft } from 'lucide-react';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';

const Cart = () => {
  const navigate = useNavigate();
  const { cartItems, updateCartItem, removeFromCart, getTotalAmount, loading } = useCart();
  const { isAuthenticated } = useAuth();

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    navigate('/login', { state: { from: { pathname: '/cart' } } });
    return null;
  }

  const handleQuantityChange = async (itemId: number, newQuantity: number) => {
    if (newQuantity < 1) return;
    await updateCartItem(itemId, newQuantity);
  };

  const handleRemoveItem = async (itemId: number) => {
    await removeFromCart(itemId);
  };

  const formatPrice = (price: string | number) => {
    return typeof price === 'string' ? parseFloat(price) : price;
  };

  if (cartItems.length === 0) {
    return (
      <div className="pt-20 min-h-screen bg-cream-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <ShoppingCart className="mx-auto h-24 w-24 text-burgundy-300" />
            <h2 className="mt-4 text-3xl font-playfair font-bold text-burgundy-800">
              Your cart is empty
            </h2>
            <p className="mt-2 text-burgundy-600">
              Start shopping to add items to your cart
            </p>
            <Button
              className="mt-6 luxury-gradient text-white hover:opacity-90"
              onClick={() => navigate('/products')}
            >
              Continue Shopping
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-20 min-h-screen bg-cream-200">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-playfair font-bold text-burgundy-800">
              Shopping Cart
            </h1>
            <p className="text-burgundy-600 mt-2">
              {cartItems.length} {cartItems.length === 1 ? 'item' : 'items'} in your cart
            </p>
          </div>
          <Button
            variant="outline"
            onClick={() => navigate('/products')}
            className="border-burgundy-300 text-burgundy-700 hover:bg-burgundy-50"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Continue Shopping
          </Button>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-4">
            {cartItems.map((item) => {
              // Safely access product data with null checks
              const categoryName = item.product?.category_name || item.product?.category?.name || 'Unknown Category';

              // Skip rendering if product data is missing
              if (!item.product) {
                console.warn('Cart item missing product data:', item);
                return null;
              }

              return (
                <Card key={item.id} className="bg-white/90 backdrop-blur-sm border-cream-300">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      {/* Product Image */}
                      <div className="flex-shrink-0">
                        <img
                          src={item.product.image_url || item.product.image || 'https://via.placeholder.com/100x100/8B1538/FFFFFF?text=No+Image'}
                          alt={item.product.name || 'Product'}
                          className="w-20 h-20 object-cover rounded-lg"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = 'https://via.placeholder.com/100x100/8B1538/FFFFFF?text=No+Image';
                          }}
                        />
                      </div>

                      {/* Product Details */}
                      <div className="flex-grow">
                        <h3 className="font-playfair font-semibold text-lg text-burgundy-800">
                          {item.product.name || 'Unknown Product'}
                        </h3>
                        <Badge className="mt-1 bg-burgundy-100 text-burgundy-800">
                          {categoryName}
                        </Badge>
                        <p className="text-burgundy-600 text-sm mt-1 line-clamp-2">
                          {item.product.description || 'No description available'}
                        </p>
                        
                        {/* Variations */}
                        {item.variation && item.variation.length > 0 && (
                          <div className="mt-2">
                            <p className="text-sm text-burgundy-600">
                              Variations: {item.variation.map(v => v.variation_value).join(', ')}
                            </p>
                          </div>
                        )}
                      </div>

                      {/* Price and Quantity */}
                      <div className="flex flex-col items-end space-y-2">
                        <div className="text-xl font-bold font-playfair text-burgundy-800">
                          Rs. {formatPrice(item.product.price || '0').toFixed(2)}
                        </div>
                        
                        {/* Quantity Controls */}
                        <div className="flex items-center space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                            disabled={item.quantity <= 1 || loading}
                            className="w-8 h-8 p-0"
                          >
                            <Minus className="w-4 h-4" />
                          </Button>
                          <span className="font-medium text-burgundy-800 min-w-[2rem] text-center">
                            {item.quantity}
                          </span>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                            disabled={loading}
                            className="w-8 h-8 p-0"
                          >
                            <Plus className="w-4 h-4" />
                          </Button>
                        </div>

                        {/* Subtotal */}
                        <div className="text-lg font-semibold text-burgundy-800">
                          Rs. {(formatPrice(item.product.price || '0') * item.quantity).toFixed(2)}
                        </div>

                        {/* Remove Button */}
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleRemoveItem(item.id)}
                          disabled={loading}
                          className="text-red-600 hover:text-red-800 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card className="bg-white/90 backdrop-blur-sm border-cream-300 sticky top-24">
              <CardHeader>
                <CardTitle className="text-xl font-playfair text-burgundy-800">
                  Order Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between text-burgundy-700">
                  <span>Subtotal</span>
                  <span>Rs. {getTotalAmount().toFixed(2)}</span>
                </div>
                
                <div className="flex justify-between text-burgundy-700">
                  <span>Shipping</span>
                  <span>Free</span>
                </div>
                
                <Separator className="bg-burgundy-200" />
                
                <div className="flex justify-between text-lg font-bold text-burgundy-800">
                  <span>Total</span>
                  <span>Rs. {getTotalAmount().toFixed(2)}</span>
                </div>
                
                <Button
                  className="w-full luxury-gradient text-white hover:opacity-90"
                  onClick={() => navigate('/checkout')}
                  disabled={loading}
                >
                  Proceed to Checkout
                </Button>
                
                <p className="text-xs text-burgundy-600 text-center">
                  Secure checkout with multiple payment options
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Cart;
