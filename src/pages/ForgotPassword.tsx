import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Mail, ArrowLeft } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const { requestPasswordReset } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toast.error('Please enter your email address');
      return;
    }

    setLoading(true);

    try {
      const success = await requestPasswordReset(email);
      if (success) {
        setEmailSent(true);
      }
    } catch (error) {
      console.error('Password reset error:', error);
    } finally {
      setLoading(false);
    }
  };

  if (emailSent) {
    return (
      <div className="pt-20 min-h-screen bg-cream-200 flex items-center justify-center">
        <div className="max-w-md w-full mx-4">
          <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-playfair text-burgundy-800">
                Check Your Email
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Mail className="w-8 h-8 text-green-600" />
                </div>
                <p className="text-burgundy-600 mb-4">
                  We've sent a password reset link to <strong>{email}</strong>
                </p>
                <p className="text-sm text-burgundy-500">
                  Please check your email and follow the instructions to reset your password.
                </p>
              </div>
              
              <div className="text-center">
                <Link 
                  to="/login" 
                  className="inline-flex items-center text-burgundy-600 hover:text-burgundy-800 transition-colors"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Login
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-20 min-h-screen bg-cream-200 flex items-center justify-center">
      <div className="max-w-md w-full mx-4">
        <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-playfair text-burgundy-800">
              Forgot Password
            </CardTitle>
            <p className="text-burgundy-600 mt-2">
              Enter your email address and we'll send you a link to reset your password.
            </p>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-burgundy-700">Email Address *</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-burgundy-400 w-4 h-4" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="pl-10 border-burgundy-200 focus:border-burgundy-400"
                    required
                  />
                </div>
              </div>

              <Button
                type="submit"
                className="w-full luxury-gradient text-white hover:opacity-90"
                disabled={loading}
              >
                {loading ? 'Sending...' : 'Send Reset Link'}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <Link 
                to="/login" 
                className="inline-flex items-center text-burgundy-600 hover:text-burgundy-800 transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Login
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ForgotPassword;
