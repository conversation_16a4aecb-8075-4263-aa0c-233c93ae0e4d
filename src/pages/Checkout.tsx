import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { ShoppingCart, CreditCard, Truck, MapPin, User, Mail, Phone } from 'lucide-react';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { getPaymentMethods, createOrder } from '@/api/apiService';
import { PaymentMethod } from '@/types/ecommerce';
import { toast } from 'react-toastify';

const Checkout = () => {
  const navigate = useNavigate();
  const { cartItems, getTotalAmount, clearCart } = useCart();
  const { user, isAuthenticated } = useAuth();
  
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    firstName: user?.first_name || '',
    lastName: user?.last_name || '',
    email: user?.email || '',
    phone: user?.phone || '',
    state: '',
    area: '',
    address: '',
    orderNote: ''
  });

  // Redirect if not authenticated or cart is empty
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { state: { from: { pathname: '/checkout' } } });
      return;
    }
    
    if (cartItems.length === 0) {
      navigate('/cart');
      return;
    }
  }, [isAuthenticated, cartItems, navigate]);

  // Fetch payment methods
  useEffect(() => {
    const fetchPaymentMethods = async () => {
      try {
        const response = await getPaymentMethods();
        if (response.success && response.data) {
          const methods = Array.isArray(response.data) ? response.data : [];
          setPaymentMethods(methods);
          if (methods.length > 0) {
            setSelectedPaymentMethod(methods[0].id.toString());
          }
        }
      } catch (error) {
        console.error('Error fetching payment methods:', error);
      }
    };

    fetchPaymentMethods();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.firstName || !formData.lastName || !formData.email || !formData.phone || 
        !formData.state || !formData.area || !formData.address) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (!selectedPaymentMethod) {
      toast.error('Please select a payment method');
      return;
    }

    setLoading(true);

    try {
      const orderData = {
        first_name: formData.firstName,
        last_name: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        state: formData.state,
        area: formData.area,
        address: formData.address,
        order_note: formData.orderNote,
        payment_method: parseInt(selectedPaymentMethod)
      };

      const response = await createOrder(orderData);
      
      if (response.status === "200") {
        toast.success('Order placed successfully!');
        clearCart();
        navigate('/order-confirmation', { 
          state: { orderData: response.data }
        });
      } else {
        toast.error(response.message || 'Failed to place order');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to place order');
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price: string | number) => {
    return typeof price === 'string' ? parseFloat(price) : price;
  };

  if (!isAuthenticated || cartItems.length === 0) {
    return null;
  }

  return (
    <div className="pt-20 min-h-screen bg-cream-200">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-playfair font-bold text-burgundy-800">
            Checkout
          </h1>
          <p className="text-burgundy-600 mt-2">
            Complete your order
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Checkout Form */}
          <div className="space-y-6">
            <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
              <CardHeader>
                <CardTitle className="flex items-center text-burgundy-800">
                  <User className="w-5 h-5 mr-2" />
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName" className="text-burgundy-700">First Name *</Label>
                    <Input
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      className="border-burgundy-200 focus:border-burgundy-400"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName" className="text-burgundy-700">Last Name *</Label>
                    <Input
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      className="border-burgundy-200 focus:border-burgundy-400"
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="email" className="text-burgundy-700">Email *</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-burgundy-400 w-4 h-4" />
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="pl-10 border-burgundy-200 focus:border-burgundy-400"
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="phone" className="text-burgundy-700">Phone *</Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-burgundy-400 w-4 h-4" />
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="pl-10 border-burgundy-200 focus:border-burgundy-400"
                      required
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
              <CardHeader>
                <CardTitle className="flex items-center text-burgundy-800">
                  <MapPin className="w-5 h-5 mr-2" />
                  Delivery Address
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="state" className="text-burgundy-700">State/Province *</Label>
                    <Input
                      id="state"
                      name="state"
                      value={formData.state}
                      onChange={handleInputChange}
                      className="border-burgundy-200 focus:border-burgundy-400"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="area" className="text-burgundy-700">Area/City *</Label>
                    <Input
                      id="area"
                      name="area"
                      value={formData.area}
                      onChange={handleInputChange}
                      className="border-burgundy-200 focus:border-burgundy-400"
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="address" className="text-burgundy-700">Full Address *</Label>
                  <Textarea
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    className="border-burgundy-200 focus:border-burgundy-400"
                    rows={3}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="orderNote" className="text-burgundy-700">Order Notes (Optional)</Label>
                  <Textarea
                    id="orderNote"
                    name="orderNote"
                    value={formData.orderNote}
                    onChange={handleInputChange}
                    className="border-burgundy-200 focus:border-burgundy-400"
                    rows={2}
                    placeholder="Any special instructions for delivery..."
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
              <CardHeader>
                <CardTitle className="flex items-center text-burgundy-800">
                  <CreditCard className="w-5 h-5 mr-2" />
                  Payment Method
                </CardTitle>
              </CardHeader>
              <CardContent>
                <RadioGroup value={selectedPaymentMethod} onValueChange={setSelectedPaymentMethod}>
                  {paymentMethods.map((method) => (
                    <div key={method.id} className="flex items-center space-x-2 p-3 border border-burgundy-200 rounded-lg">
                      <RadioGroupItem value={method.id.toString()} id={method.id.toString()} />
                      <Label htmlFor={method.id.toString()} className="flex-1 cursor-pointer">
                        <div className="flex items-center space-x-3">
                          {method.image && (
                            <img src={method.image} alt={method.name} className="w-8 h-8 object-contain" />
                          )}
                          <span className="text-burgundy-800">{method.name}</span>
                        </div>
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div>
            <Card className="bg-white/90 backdrop-blur-sm border-cream-300 sticky top-24">
              <CardHeader>
                <CardTitle className="flex items-center text-burgundy-800">
                  <ShoppingCart className="w-5 h-5 mr-2" />
                  Order Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Cart Items */}
                <div className="space-y-3 max-h-60 overflow-y-auto">
                  {cartItems.map((item) => {
                    const categoryName = item.product.category_name;
                    
                    return (
                      <div key={item.id} className="flex items-center space-x-3 p-2 border border-cream-200 rounded">
                        <img
                          src={item.product.image_url || item.product.image || 'https://via.placeholder.com/50x50/8B1538/FFFFFF?text=No+Image'}
                          alt={item.product.name}
                          className="w-12 h-12 object-cover rounded"
                        />
                        <div className="flex-1">
                          <h4 className="font-medium text-burgundy-800 text-sm">{item.product.name}</h4>
                          <Badge className="text-xs bg-burgundy-100 text-burgundy-800">{categoryName}</Badge>
                          <p className="text-xs text-burgundy-600">Qty: {item.quantity}</p>
                        </div>
                        <div className="text-sm font-semibold text-burgundy-800">
                          Rs. {(formatPrice(item.product.price) * item.quantity).toFixed(2)}
                        </div>
                      </div>
                    );
                  })}
                </div>
                
                <Separator className="bg-burgundy-200" />
                
                <div className="space-y-2">
                  <div className="flex justify-between text-burgundy-700">
                    <span>Subtotal</span>
                    <span>Rs. {getTotalAmount().toFixed(2)}</span>
                  </div>
                  
                  <div className="flex justify-between text-burgundy-700">
                    <span>Shipping</span>
                    <span>Free</span>
                  </div>
                  
                  <Separator className="bg-burgundy-200" />
                  
                  <div className="flex justify-between text-lg font-bold text-burgundy-800">
                    <span>Total</span>
                    <span>Rs. {getTotalAmount().toFixed(2)}</span>
                  </div>
                </div>
                
                <Button
                  onClick={handleSubmit}
                  className="w-full luxury-gradient text-white hover:opacity-90"
                  disabled={loading}
                >
                  <Truck className="w-4 h-4 mr-2" />
                  {loading ? 'Placing Order...' : 'Place Order'}
                </Button>
                
                <p className="text-xs text-burgundy-600 text-center">
                  By placing this order, you agree to our terms and conditions
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Checkout;
