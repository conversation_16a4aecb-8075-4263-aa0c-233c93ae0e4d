import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  Clock, 
  Star, 
  Award, 
  CheckCircle, 
  Users, 
  ArrowLeft,
  Calendar,
  Phone,
  ChevronLeft,
  ChevronRight,
  Maximize2,
  X
} from 'lucide-react';
// Services functionality removed - not available in backend
import { Service } from '@/data/mockData';

const ServiceDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [service, setService] = useState<Service | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [selectedImageSet, setSelectedImageSet] = useState<{
    before: string;
    after: string;
    index: number;
  } | null>(null);

  // Fetch service details
  useEffect(() => {
    const fetchService = async () => {
      if (!id) return;

      try {
        setLoading(true);
        // Services functionality removed - not available in backend
        setService(null);
      } catch (error) {
        // Error handled silently
      } finally {
        setLoading(false);
      }
    };

    fetchService();
  }, [id]);

  // Memoized image gallery navigation
  const nextImage = useMemo(() => () => {
    const images = service?.before_after_images || service?.beforeAfterImages;
    if (images && images.length > 0) {
      setCurrentImageIndex((prev) =>
        (prev + 1) % images.length
      );
    }
  }, [service?.before_after_images, service?.beforeAfterImages]);

  const prevImage = useMemo(() => () => {
    const images = service?.before_after_images || service?.beforeAfterImages;
    if (images && images.length > 0) {
      setCurrentImageIndex((prev) =>
        (prev - 1 + images.length) % images.length
      );
    }
  }, [service?.before_after_images, service?.beforeAfterImages]);

  const handleBookConsultation = () => {
    navigate('/contact', {
      state: {
        selectedService: service?.name,
        serviceId: service?.id
      }
    });
  };

  const handleImageClick = (beforeImage: string, afterImage: string, index: number) => {
    setSelectedImageSet({
      before: beforeImage,
      after: afterImage,
      index: index
    });
  };

  const closeModal = () => {
    setSelectedImageSet(null);
  };

  if (loading) {
    return (
      <div className="pt-20 min-h-screen bg-cream-200 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-burgundy-600 mx-auto mb-4"></div>
          <p className="text-burgundy-600">Loading service details...</p>
        </div>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="pt-20 min-h-screen bg-cream-200 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-burgundy-800 mb-4">Service Not Found</h2>
          <Button onClick={() => navigate('/services')}>
            Back to Services
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-20 min-h-screen bg-cream-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        
        {/* Back Navigation */}
        <Button 
          variant="ghost" 
          className="mb-8 text-burgundy-600 hover:text-burgundy-800"
          onClick={() => navigate('/services')}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Services
        </Button>

        {/* Service Header */}
        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          <div>
            <div className="relative rounded-2xl overflow-hidden">
              <img
                src={service.image_url || service.image || 'https://via.placeholder.com/400x400/8B1538/FFFFFF?text=No+Image'}
                alt={service.name}
                className="w-full h-96 object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = 'https://via.placeholder.com/400x400/8B1538/FFFFFF?text=No+Image';
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-burgundy-900/50 to-transparent"></div>
              
              {service.popular && (
                <Badge className="absolute top-4 left-4 bg-gold-500 text-white">
                  Most Popular
                </Badge>
              )}

              <div className="absolute bottom-4 left-4 text-white">
                <div className="text-3xl font-bold font-playfair">Rs. {service.price}</div>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div>
              <Badge className="bg-burgundy-100 text-burgundy-800 mb-4">
                {service.category_name || service.category}
              </Badge>
              <h1 className="text-4xl font-bold font-playfair text-burgundy-800 mb-4">
                {service.name}
              </h1>
              <p className="text-xl text-burgundy-600 leading-relaxed">
                {service.description}
              </p>
            </div>

            <div className="grid grid-cols-2 gap-6">
              <div className="flex items-center text-burgundy-600">
                <Clock className="w-5 h-5 mr-3" />
                <div>
                  <div className="font-semibold">Duration</div>
                  <div>{service.duration}</div>
                </div>
              </div>

              <div className="flex items-center text-burgundy-600">
                <Star className="w-5 h-5 mr-3 fill-gold-400 text-gold-400" />
                <div>
                  <div className="font-semibold">Rating</div>
                  <div>4.8+ rating</div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <Button 
                size="lg" 
                className="w-full luxury-gradient text-white hover:opacity-90"
                onClick={handleBookConsultation}
              >
                <Calendar className="w-5 h-5 mr-2" />
                Book Consultation
              </Button>
              
              <Button 
                variant="outline" 
                size="lg" 
                className="w-full border-burgundy-300 text-burgundy-700 hover:bg-burgundy-50"
                onClick={() => window.location.href = 'tel:+919876543210'}
              >
                <Phone className="w-5 h-5 mr-2" />
                Call Now
              </Button>
            </div>
          </div>
        </div>

        {/* Service Process */}
        {(service.process_steps || service.process) && (
          <Card className="mb-16 bg-white/90 backdrop-blur-sm border-cream-300">
            <CardContent className="p-8">
              <h2 className="text-3xl font-bold font-playfair text-burgundy-800 mb-8 text-center">
                Service Process
              </h2>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {(service.process_steps || service.process)?.map((step, index) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="w-8 h-8 luxury-gradient rounded-full flex items-center justify-center text-white font-bold shrink-0">
                      {index + 1}
                    </div>
                    <p className="text-burgundy-600">{step}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Benefits & Staff Info */}
        <div className="grid lg:grid-cols-2 gap-8 mb-16">
          {/* Benefits */}
          {service.benefits && (
            <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center mr-4">
                    <CheckCircle className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold font-playfair text-burgundy-800">
                    Key Benefits
                  </h3>
                </div>
                <ul className="space-y-3">
                  {service.benefits.map((benefit, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 shrink-0" />
                      <span className="text-burgundy-600">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Staff & Equipment */}
          <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
            <CardContent className="p-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center mr-4">
                  <Users className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold font-playfair text-burgundy-800">
                  Expertise & Equipment
                </h3>
              </div>
              
              {(service.staff_qualification || service.staff) && (
                <div className="mb-6">
                  <h4 className="font-semibold text-burgundy-700 mb-2">Our Specialist</h4>
                  <p className="text-burgundy-600">{service.staff_qualification || service.staff}</p>
                </div>
              )}

              {(service.equipment_used || service.equipment) && (
                <div>
                  <h4 className="font-semibold text-burgundy-700 mb-2">Professional Equipment</h4>
                  <ul className="space-y-1">
                    {(service.equipment_used || service.equipment)?.map((item, index) => (
                      <li key={index} className="text-burgundy-600 flex items-center">
                        <Award className="w-4 h-4 mr-2 text-gold-500" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Before/After Gallery */}
        {((service.before_after_images && service.before_after_images.length > 0) ||
          (service.beforeAfterImages && service.beforeAfterImages.length > 0)) && (
          <Card className="mb-16 bg-white/90 backdrop-blur-sm border-cream-300">
            <CardContent className="p-8">
              <h2 className="text-3xl font-bold font-playfair text-burgundy-800 mb-8 text-center">
                Before & After Results
              </h2>

              <div className="relative max-w-4xl mx-auto">
                {service.before_after_images && service.before_after_images.length > 0 ? (
                  // New format: Array of before/after image objects
                  <div className="space-y-8">
                    {service.before_after_images.map((imageSet, index) => (
                      <div key={imageSet.id || index} className="grid md:grid-cols-2 gap-8">
                        <div>
                          <h3 className="text-lg font-semibold text-burgundy-700 mb-4 text-center">Before</h3>
                          <div
                            className="relative group cursor-pointer"
                            onClick={() => handleImageClick(
                              imageSet.before_image_url || imageSet.before_image,
                              imageSet.after_image_url || imageSet.after_image,
                              index
                            )}
                          >
                            <img
                              src={imageSet.before_image_url || imageSet.before_image}
                              alt={`Before treatment ${index + 1}`}
                              className="w-full h-64 object-cover rounded-lg transition-transform duration-300 group-hover:scale-105"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = 'https://via.placeholder.com/400x300/8B1538/FFFFFF?text=Before+Image';
                              }}
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-lg flex items-center justify-center">
                              <Maximize2 className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                            </div>
                          </div>
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-burgundy-700 mb-4 text-center">After</h3>
                          <div
                            className="relative group cursor-pointer"
                            onClick={() => handleImageClick(
                              imageSet.before_image_url || imageSet.before_image,
                              imageSet.after_image_url || imageSet.after_image,
                              index
                            )}
                          >
                            <img
                              src={imageSet.after_image_url || imageSet.after_image}
                              alt={`After treatment ${index + 1}`}
                              className="w-full h-64 object-cover rounded-lg transition-transform duration-300 group-hover:scale-105"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = 'https://via.placeholder.com/400x300/8B1538/FFFFFF?text=After+Image';
                              }}
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-lg flex items-center justify-center">
                              <Maximize2 className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : service.beforeAfterImages && service.beforeAfterImages.length > 0 ? (
                  // Old format: Before/After objects
                  <div>
                    <div className="grid md:grid-cols-2 gap-8">
                      <div>
                        <h3 className="text-lg font-semibold text-burgundy-700 mb-4 text-center">Before</h3>
                        <img
                          src={service.beforeAfterImages[currentImageIndex].before}
                          alt="Before treatment"
                          className="w-full h-64 object-cover rounded-lg"
                        />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-burgundy-700 mb-4 text-center">After</h3>
                        <img
                          src={service.beforeAfterImages[currentImageIndex].after}
                          alt="After treatment"
                          className="w-full h-64 object-cover rounded-lg"
                        />
                      </div>
                    </div>

                    {service.beforeAfterImages.length > 1 && (
                      <div className="flex justify-center mt-6 space-x-4">
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={prevImage}
                          className="border-burgundy-300 text-burgundy-700 hover:bg-burgundy-50"
                        >
                          <ChevronLeft className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={nextImage}
                          className="border-burgundy-300 text-burgundy-700 hover:bg-burgundy-50"
                        >
                          <ChevronRight className="w-4 h-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                ) : null}
              </div>
            </CardContent>
          </Card>
        )}

        {/* FAQ Section */}
        {service.faq && service.faq.length > 0 && (
          <Card className="mb-16 bg-white/90 backdrop-blur-sm border-cream-300">
            <CardContent className="p-8">
              <h2 className="text-3xl font-bold font-playfair text-burgundy-800 mb-8 text-center">
                Frequently Asked Questions
              </h2>
              
              <div className="space-y-6">
                {service.faq.map((item, index) => (
                  <div key={index} className="border-b border-cream-300 pb-6 last:border-b-0">
                    <h3 className="text-lg font-semibold text-burgundy-800 mb-3">
                      {item.question}
                    </h3>
                    <p className="text-burgundy-600 leading-relaxed">
                      {item.answer}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* CTA Section */}
        <div className="text-center luxury-gradient rounded-2xl p-12 text-white">
          <h2 className="text-3xl font-bold font-playfair mb-4">
            Ready to Experience {service.name}?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Book your consultation today and let our experts take care of you
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="bg-white text-burgundy-800 hover:bg-cream-100 px-8 py-4 text-lg"
              onClick={handleBookConsultation}
            >
              <Calendar className="w-5 h-5 mr-2" />
              Book Consultation
            </Button>
            <Button 
              variant="outline" 
              size="lg" 
              className="border-white text-burgundy-800 hover:text-white hover:bg-white/10 px-8 py-4 text-lg"
              onClick={() => navigate('/services')}
            >
              View All Services
            </Button>
          </div>
        </div>

        {/* Image Comparison Modal */}
        <Dialog open={!!selectedImageSet} onOpenChange={closeModal}>
          <DialogContent className="max-w-6xl bg-white p-0 overflow-hidden">
            <DialogHeader className="p-6 pb-0">
              <DialogTitle className="text-2xl font-playfair text-burgundy-800 text-center">
                Before & After Comparison {selectedImageSet && `- Set ${selectedImageSet.index + 1}`}
              </DialogTitle>
            </DialogHeader>

            {selectedImageSet && (
              <div className="p-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold text-burgundy-700 mb-4 text-center">Before</h3>
                    <img
                      src={selectedImageSet.before}
                      alt={`Before treatment ${selectedImageSet.index + 1}`}
                      className="w-full h-80 object-cover rounded-lg"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = 'https://via.placeholder.com/600x400/8B1538/FFFFFF?text=Before+Image';
                      }}
                    />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-burgundy-700 mb-4 text-center">After</h3>
                    <img
                      src={selectedImageSet.after}
                      alt={`After treatment ${selectedImageSet.index + 1}`}
                      className="w-full h-80 object-cover rounded-lg"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = 'https://via.placeholder.com/600x400/8B1538/FFFFFF?text=After+Image';
                      }}
                    />
                  </div>
                </div>

                <div className="flex justify-center mt-6">
                  <Button
                    onClick={closeModal}
                    className="luxury-gradient text-white hover:opacity-90 px-8 py-2"
                  >
                    <X className="w-4 h-4 mr-2" />
                    Close
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default ServiceDetail;