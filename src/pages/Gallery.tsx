
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
// Gallery functionality removed - not available in backend
import { GalleryImage } from '@/data/mockData';

const Gallery = () => {
  const [images, setImages] = useState<GalleryImage[]>([]);
  const [filteredImages, setFilteredImages] = useState<GalleryImage[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch gallery images
  useEffect(() => {
    const fetchImages = async () => {
      try {
        setLoading(true);
        // Gallery functionality removed - not available in backend
        const response = { success: false };
        if (response && response.success) {
          const images = response.data || [];
          setImages(images);
          setFilteredImages(images);
        }
      } catch (error) {
        setImages([]);
        setFilteredImages([]);
      } finally {
        setLoading(false);
      }
    };

    fetchImages();
  }, []);

  // Filter images when category changes
  useEffect(() => {
    if (selectedCategory === 'all') {
      setFilteredImages(images);
    } else {
      setFilteredImages(images.filter(img => img.category === selectedCategory));
    }
  }, [selectedCategory, images]);

  const categories = [
    { value: 'all', label: 'All Work' },
    { value: 'facial', label: 'Facial Treatments' },
    { value: 'hair', label: 'Hair Services' },
    { value: 'makeup', label: 'Makeup Artistry' }
  ];

  return (
    <div className="pt-20 min-h-screen bg-cream-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold font-playfair text-burgundy-800 mb-4">
            Our Work Gallery
          </h1>
          <p className="text-xl text-burgundy-600 max-w-2xl mx-auto">
            Explore our portfolio of stunning beauty transformations and luxury treatments
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-3 mb-12">
          {categories.map((category) => (
            <Button
              key={category.value}
              variant={selectedCategory === category.value ? "default" : "outline"}
              className={`transition-all duration-300 ${
                selectedCategory === category.value
                  ? 'luxury-gradient text-white'
                  : 'border-burgundy-300 text-burgundy-700 hover:bg-burgundy-50'
              }`}
              onClick={() => setSelectedCategory(category.value)}
            >
              {category.label}
            </Button>
          ))}
        </div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {loading ? (
            // Loading skeleton
            Array.from({ length: 9 }).map((_, index) => (
              <div key={index} className="bg-white/50 rounded-lg h-64 animate-pulse"></div>
            ))
          ) : (
            filteredImages.map((image, index) => (
              <div
                key={index}
                className="group relative overflow-hidden rounded-lg cursor-pointer hover-lift"
                onClick={() => setSelectedImage(image)}
              >
                <img
                  src={image.url}
                  alt={image.alt}
                  className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110"
                />
                
                <div className="absolute inset-0 bg-gradient-to-t from-burgundy-900/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 text-white">
                    <Badge className="mb-2 bg-gold-500">
                      {categories.find(cat => cat.value === image.category)?.label}
                    </Badge>
                    <h3 className="text-lg font-semibold font-playfair">
                      {image.alt}
                    </h3>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* No results */}
        {filteredImages.length === 0 && (
          <div className="text-center py-12">
            <div className="text-burgundy-600 text-lg">
              No images found in this category.
            </div>
          </div>
        )}

        {/* CTA Section */}
        <div className="mt-20 text-center luxury-gradient rounded-2xl p-12 text-white">
          <h2 className="text-3xl font-bold font-playfair mb-4">
            Ready for Your Transformation?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Let us create your perfect look with our premium beauty services
          </p>
          <Button 
            size="lg" 
            className="bg-white text-burgundy-800 hover:bg-cream-100 px-8 py-4 text-lg"
            onClick={() => window.location.href = '/contact'}
          >
            Book Your Session
          </Button>
        </div>

        {/* Lightbox Modal */}
        <Dialog open={!!selectedImage} onOpenChange={() => setSelectedImage(null)}>
          <DialogContent className="max-w-4xl bg-transparent border-none p-0">
            {selectedImage && (
              <img
                src={selectedImage.url}
                alt={selectedImage.alt}
                className="w-full h-auto max-h-[90vh] object-contain rounded-lg"
              />
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default Gallery;
