
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Clock, Star, Award } from 'lucide-react';

const Services = () => {
  const navigate = useNavigate();






  return (
    <div className="pt-20 min-h-screen bg-cream-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">

        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold font-playfair text-burgundy-800 mb-4">
            Premium Beauty Services
          </h1>
          <p className="text-xl text-burgundy-600 max-w-2xl mx-auto">
            Experience luxury beauty treatments performed by certified professionals using premium products
          </p>
        </div>

        {/* Coming Soon Message */}
        <div className="text-center py-20">
          <div className="w-24 h-24 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-8">
            <Star className="w-12 h-12 text-white" />
          </div>
          <h2 className="text-4xl font-bold font-playfair text-burgundy-800 mb-4">
            Services Coming Soon
          </h2>
          <p className="text-xl text-burgundy-600 max-w-2xl mx-auto mb-8">
            We're working hard to bring you amazing beauty services. In the meantime, explore our premium beauty products!
          </p>
          <Button
            size="lg"
            className="luxury-gradient text-white hover:opacity-90 px-8 py-4 text-lg"
            onClick={() => navigate('/products')}
          >
            <Star className="w-5 h-5 mr-2" />
            Explore Products
          </Button>
        </div>

        {/* Service Benefits */}
        <div className="mt-20 cream-gradient rounded-2xl p-12">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold font-playfair text-burgundy-800 mb-4">
              Why Choose Our Services
            </h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold font-playfair text-burgundy-800 mb-2">
                Certified Professionals
              </h3>
              <p className="text-burgundy-600">
                All our beauticians are certified and have years of experience in luxury beauty treatments
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold font-playfair text-burgundy-800 mb-2">
                Premium Products
              </h3>
              <p className="text-burgundy-600">
                We use only the finest beauty products from renowned international brands
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold font-playfair text-burgundy-800 mb-2">
                Personalized Experience
              </h3>
              <p className="text-burgundy-600">
                Each treatment is customized to your specific needs and preferences
              </p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center luxury-gradient rounded-2xl p-12 text-white">
          <h2 className="text-3xl font-bold font-playfair mb-4">
            Ready to Book Your Service?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Contact us today to schedule your appointment and experience luxury beauty care
          </p>
          <Button 
            size="lg" 
            className="bg-white text-burgundy-800 hover:bg-cream-100 px-8 py-4 text-lg"
            onClick={() => navigate('/contact')}
          >
            Contact Us Now
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Services;
