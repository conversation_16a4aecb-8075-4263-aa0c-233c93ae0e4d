import React, { useMemo, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Hero from '@/components/Hero';
import ProductCard from '@/components/ProductCard';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Star, Award, Users, Clock, ArrowRight, ChevronLeft, ChevronRight, Sparkles, Scissors, Heart } from 'lucide-react';
import { getProductsFromBackend, getTestimonials } from '@/api/apiService';
import { Product } from '@/types/ecommerce';
import { ServiceCategory, Testimonial } from '@/data/mockData';

const Home = () => {
  const navigate = useNavigate();
  
  // States for featured content
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [featuredServices, setFeaturedServices] = useState<ServiceCategory[]>([]);
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [loading, setLoading] = useState(true);

  // Memoized data fetching
  const fetchData = useMemo(() => async () => {
    try {
      setLoading(true);
      
      // Fetch all products and take first 6
      const productsResponse = await getProductsFromBackend();
      if (productsResponse && productsResponse.success) {
        const products = productsResponse.data || [];
        setFeaturedProducts(products.slice(0, 6));
      }

      // Services functionality removed - not available in backend
      setFeaturedServices([]);

      // Fetch all testimonials and take first 6
      const testimonialsResponse = await getTestimonials();
      if (testimonialsResponse && testimonialsResponse.success) {
        const testimonials = testimonialsResponse.data || [];
        setTestimonials(testimonials.slice(0, 6));
      }
    } catch (error) {
      // Set empty arrays on error to prevent undefined errors
      setFeaturedProducts([]);
      setFeaturedServices([]);
      setTestimonials([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Auto-play testimonial carousel
  useEffect(() => {
    if (!isAutoPlaying) return;
    
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, testimonials.length]);

  // Memoized handlers
  const handleProductDetails = useMemo(() => (product: any) => {
    navigate('/products');
  }, [navigate]);


  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  return (
    <div className="pt-16">
      <Hero />
      
      {/* Featured Products Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold font-playfair text-burgundy-800 mb-4">
              Featured Products
            </h2>
            <p className="text-xl text-burgundy-600 max-w-2xl mx-auto">
              Discover our most popular luxury beauty products
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {loading ? (
              // Loading skeleton
              Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="bg-white/50 rounded-lg h-96 animate-pulse"></div>
              ))
            ) : (
              featuredProducts.slice(0, 6).map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  onViewDetails={handleProductDetails}
                />
              ))
            )}
          </div>

          <div className="text-center">
            <Button 
              size="lg"
              onClick={() => navigate('/products')}
              className="luxury-gradient text-white hover:opacity-90 px-8 py-4 text-lg"
            >
              View All Products
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
          </div>
        </div>
      </section>

      

      {/* Why Choose Us */}
      <section className="py-20 cream-gradient">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold font-playfair text-burgundy-800 mb-4">
              Why Choose Mah Beauty
            </h2>
          </div>

          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold font-playfair text-burgundy-800 mb-2">
                Expert Professionals
              </h3>
              <p className="text-burgundy-600">
                Certified and experienced beauty experts
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold font-playfair text-burgundy-800 mb-2">
                Premium Products
              </h3>
              <p className="text-burgundy-600">
                Only the finest beauty products and tools
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold font-playfair text-burgundy-800 mb-2">
                Personalized Care
              </h3>
              <p className="text-burgundy-600">
                Tailored treatments for your unique needs
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold font-playfair text-burgundy-800 mb-2">
                Flexible Timing
              </h3>
              <p className="text-burgundy-600">
                Convenient appointment scheduling
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Large Testimonial Carousel */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold font-playfair text-burgundy-800 mb-4">
              What Our Clients Say
            </h2>
            <p className="text-xl text-burgundy-600">
              Real experiences from our valued customers
            </p>
          </div>

          <div 
            className="relative max-w-4xl mx-auto"
            onMouseEnter={() => setIsAutoPlaying(false)}
            onMouseLeave={() => setIsAutoPlaying(true)}
          >
            {testimonials.length > 0 && testimonials[currentTestimonial] && (
              <Card className="bg-cream-100 border-cream-300 overflow-hidden">
                <CardContent className="p-12 text-center">
                  <div className="mb-8">
                    <img
                      src={testimonials[currentTestimonial].image}
                      alt={testimonials[currentTestimonial].name}
                      className="w-24 h-24 rounded-full mx-auto mb-6 object-cover border-4 border-gold-400"
                    />
                    <div className="flex items-center justify-center mb-6">
                      {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                        <Star key={i} className="w-6 h-6 fill-gold-400 text-gold-400 mx-1" />
                      ))}
                    </div>
                  </div>

                  <blockquote className="text-2xl font-playfair text-burgundy-800 mb-8 leading-relaxed">
                    "{testimonials[currentTestimonial].comment}"
                  </blockquote>

                  <div className="border-t border-cream-300 pt-6">
                    <div className="font-semibold text-xl text-burgundy-800 mb-2">
                      {testimonials[currentTestimonial].name}
                    </div>
                    <div className="text-burgundy-600">
                      {testimonials[currentTestimonial].service}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Navigation Arrows */}
            {testimonials.length > 0 && (
              <>
                <Button
                  variant="outline"
                  size="icon"
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm border-burgundy-200 hover:bg-burgundy-50"
                  onClick={prevTestimonial}
                >
                  <ChevronLeft className="w-5 h-5 text-burgundy-800" />
                </Button>

                <Button
                  variant="outline"
                  size="icon"
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm border-burgundy-200 hover:bg-burgundy-50"
                  onClick={nextTestimonial}
                >
                  <ChevronRight className="w-5 h-5 text-burgundy-800" />
                </Button>

                {/* Navigation Dots */}
                <div className="flex justify-center mt-8 space-x-2">
                  {testimonials.map((_, index) => (
                    <button
                      key={index}
                      className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                        index === currentTestimonial
                          ? 'bg-burgundy-800'
                          : 'bg-burgundy-200 hover:bg-burgundy-400'
                      }`}
                      onClick={() => setCurrentTestimonial(index)}
                      aria-label={`Go to testimonial ${index + 1}`}
                    />
                  ))}
                </div>
              </>
            )}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 luxury-gradient text-white">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl font-bold font-playfair mb-4">
            Ready to Transform Your Look?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Book your appointment today and experience the luxury of premium beauty care
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="bg-white text-burgundy-800 hover:bg-cream-100 px-8 py-4 text-lg"
              onClick={() => navigate('/contact')}
            >
              Book Appointment
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-2 border-white text-burgundy-800 hover:bg-burgundy-800 hover:text-white px-8 py-4 text-lg"
              onClick={() => window.open('tel:+977-9800000000', '_self')}
            >
              Call Now
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;