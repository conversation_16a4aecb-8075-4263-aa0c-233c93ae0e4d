
import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Award, Users, Clock, Star } from 'lucide-react';
import { getTeamMembers, getAchievements, getAboutContent } from '@/api/apiService';

// Define types for the data structures
interface TeamMember {
  id: number;
  name: string;
  role: string;
  image?: string;
  image_url?: string;
  experience: string;
  specialization: string;
  certifications: string[];
  bio?: string;
}

interface Achievement {
  id: number;
  title: string;
  description: string;
  icon: string;
}

interface AboutContent {
  id: number;
  content_type: string;
  title: string;
  content: string;
  subtitle?: string;
  image_url?: string;
}

const About = () => {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [aboutContent, setAboutContent] = useState<AboutContent[]>([]);
  const [loading, setLoading] = useState(true);

  // Icon mapping for achievements
  const iconMap: { [key: string]: any } = {
    Award,
    Users,
    Star,
    Clock,
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch all data in parallel
        const [teamResponse, achievementsResponse, contentResponse] = await Promise.all([
          getTeamMembers(),
          getAchievements(),
          getAboutContent(),
        ]);

        if (teamResponse.success) {
          setTeamMembers(teamResponse.data);
        }

        if (achievementsResponse.success) {
          setAchievements(achievementsResponse.data);
        }

        if (contentResponse.success) {
          setAboutContent(contentResponse.data);
        }
      } catch (error) {
        // Error handled silently
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Helper function to get content by type
  const getContentByType = (type: string) => {
    return aboutContent.find(content => content.content_type === type);
  };

  // Default fallback images for team members
  const defaultImages = [
    'https://images.unsplash.com/photo-1594824388853-3d8f6ff7b572?q=80&w=2069&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?q=80&w=2070&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?q=80&w=2069&auto=format&fit=crop',
  ];

  if (loading) {
    return (
      <div className="pt-20 min-h-screen bg-cream-200 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-burgundy-800 mx-auto"></div>
          <p className="mt-4 text-burgundy-600">Loading about page...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-20 min-h-screen bg-cream-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold font-playfair text-burgundy-800 mb-4">
            {getContentByType('header')?.title || 'About Mah Beauty'}
          </h1>
          <p className="text-xl text-burgundy-600 max-w-3xl mx-auto">
            {getContentByType('header')?.subtitle || 'Where passion meets expertise to create extraordinary beauty experiences that celebrate your unique elegance'}
          </p>
        </div>

        {/* Story Section */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-20">
          <div className="space-y-6">
            <h2 className="text-3xl font-bold font-playfair text-burgundy-800">
              {getContentByType('story')?.title || 'Our Story'}
            </h2>
            {getContentByType('story')?.content ? (
              getContentByType('story')!.content.split('\n\n').map((paragraph, index) => (
                <p key={index} className="text-burgundy-600 leading-relaxed">
                  {paragraph}
                </p>
              ))
            ) : (
              <>
                <p className="text-burgundy-600 leading-relaxed">
                  Founded in 2025, Mah Beauty began as a dream to create a sanctuary where beauty meets luxury.
                  Our founder, Priya Malhotra, envisioned a space where every client could experience the finest
                  beauty treatments in an atmosphere of elegance and sophistication.
                </p>
                <p className="text-burgundy-600 leading-relaxed">
                  Over the years, we have grown into one of the most trusted names in luxury beauty care,
                  serving hundreds of satisfied clients and building lasting relationships based on trust,
                  quality, and exceptional service.
                </p>
                <p className="text-burgundy-600 leading-relaxed">
                  Today, we continue to set new standards in beauty care, combining traditional techniques
                  with modern innovations to deliver results that exceed expectations.
                </p>
              </>
            )}
          </div>
          
          <div className="relative">
            <img
              src="https://images.unsplash.com/photo-1560066984-138dadb4c035?q=80&w=2074&auto=format&fit=crop"
              alt="Mah Beauty Salon"
              className="w-full h-96 object-cover rounded-2xl shadow-2xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-burgundy-900/30 to-transparent rounded-2xl"></div>
          </div>
        </div>

        {/* Mission & Vision */}
        <div className="grid md:grid-cols-2 gap-8 mb-20">
          <Card className="bg-white/90 backdrop-blur-sm border-cream-300 p-8">
            <CardContent className="space-y-4 p-0">
              <div className="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center">
                <Award className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-2xl font-bold font-playfair text-burgundy-800">
                {getContentByType('mission')?.title || 'Our Mission'}
              </h3>
              <p className="text-burgundy-600 leading-relaxed">
                {getContentByType('mission')?.content || 'To provide exceptional beauty services that enhance natural beauty while creating a luxurious and relaxing experience for every client. We are committed to using only the finest products and maintaining the highest standards of hygiene and professionalism.'}
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white/90 backdrop-blur-sm border-cream-300 p-8">
            <CardContent className="space-y-4 p-0">
              <div className="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center">
                <Star className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-2xl font-bold font-playfair text-burgundy-800">
                {getContentByType('vision')?.title || 'Our Vision'}
              </h3>
              <p className="text-burgundy-600 leading-relaxed">
                {getContentByType('vision')?.content || 'To be the premier destination for luxury beauty care, recognized for our innovation, expertise, and commitment to client satisfaction. We envision expanding our services while maintaining the personalized attention that sets us apart.'}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Achievements */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold font-playfair text-burgundy-800 mb-4">
              Our Achievements
            </h2>
            <p className="text-xl text-burgundy-600">
              Recognition and milestones that reflect our commitment to excellence
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {achievements.map((achievement, index) => {
              const IconComponent = iconMap[achievement.icon] || Award;
              return (
                <Card key={achievement.id} className="text-center bg-white/90 backdrop-blur-sm border-cream-300 hover-lift">
                  <CardContent className="p-6 space-y-4">
                    <div className="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto">
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-lg font-semibold font-playfair text-burgundy-800">
                      {achievement.title}
                    </h3>
                    <p className="text-burgundy-600 text-sm">
                      {achievement.description}
                    </p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Team Section */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold font-playfair text-burgundy-800 mb-4">
              Meet Our Expert Team
            </h2>
            <p className="text-xl text-burgundy-600">
              Certified professionals dedicated to bringing out your natural beauty
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <Card key={member.id} className="bg-white/90 backdrop-blur-sm border-cream-300 hover-lift overflow-hidden">
                <div className="relative">
                  <img
                    src={member.image_url || member.image || defaultImages[index % defaultImages.length]}
                    alt={member.name}
                    className="w-full h-64 object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-burgundy-900/60 to-transparent"></div>
                  <Badge className="absolute top-3 right-3 bg-gold-500">
                    {member.experience}
                  </Badge>
                </div>

                <CardContent className="p-6 space-y-4">
                  <div>
                    <h3 className="text-xl font-bold font-playfair text-burgundy-800">
                      {member.name}
                    </h3>
                    <p className="text-burgundy-600 font-medium">
                      {member.role}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm text-burgundy-600 mb-2">
                      <strong>Specialization:</strong> {member.specialization}
                    </p>
                  </div>

                  <div className="space-y-1">
                    <p className="text-sm font-medium text-burgundy-700">Certifications:</p>
                    {member.certifications && member.certifications.map((cert, certIndex) => (
                      <Badge key={certIndex} variant="outline" className="text-xs mr-1 mb-1">
                        {cert}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Values Section */}
        <div className="cream-gradient rounded-2xl p-12">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold font-playfair text-burgundy-800 mb-4">
              {getContentByType('values')?.title || 'Our Core Values'}
            </h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {getContentByType('values')?.content ? (
              getContentByType('values')!.content.split('\n\n').map((value, index) => {
                const [title, description] = value.split(': ');
                return (
                  <div key={index} className="text-center">
                    <h3 className="text-xl font-semibold font-playfair text-burgundy-800 mb-3">
                      {title}
                    </h3>
                    <p className="text-burgundy-600">
                      {description}
                    </p>
                  </div>
                );
              })
            ) : (
              <>
                <div className="text-center">
                  <h3 className="text-xl font-semibold font-playfair text-burgundy-800 mb-3">
                    Quality Excellence
                  </h3>
                  <p className="text-burgundy-600">
                    We never compromise on quality, using only premium products and maintaining the highest standards in all our services.
                  </p>
                </div>

                <div className="text-center">
                  <h3 className="text-xl font-semibold font-playfair text-burgundy-800 mb-3">
                    Client-Centered Approach
                  </h3>
                  <p className="text-burgundy-600">
                    Every client is unique, and we tailor our services to meet individual needs and preferences with personalized attention.
                  </p>
                </div>

                <div className="text-center">
                  <h3 className="text-xl font-semibold font-playfair text-burgundy-800 mb-3">
                    Continuous Innovation
                  </h3>
                  <p className="text-burgundy-600">
                    We stay updated with the latest beauty trends and techniques to offer our clients the most advanced treatments available.
                  </p>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default About;
