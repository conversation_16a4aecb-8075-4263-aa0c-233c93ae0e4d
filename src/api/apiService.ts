import { toast } from "react-toastify";
import 'react-toastify/dist/ReactToastify.css';
import {
  mockProducts,
  mockServiceCategories,
  mockTestimonials,
  mockGalleryImages,
  Product,
  Service,
  ServiceCategory,
  Testimonial,
  GalleryImage
} from '@/data/mockData';

// Configuration for Django API 
const API_BASE_URL = 'https://mah-beauty.com/backend/api';
const USE_MOCK_DATA = false; // Set to true for development with mock data

// Auth token management
let authToken: string | null = localStorage.getItem('authToken');

export const setAuthToken = (token: string | null) => {
  authToken = token;
  if (token) {
    localStorage.setItem('authToken', token);
  } else {
    localStorage.removeItem('authToken');
  }
};

export const getAuthToken = () => {
  // Always get the latest token from localStorage
  authToken = localStorage.getItem('authToken');
  return authToken;
};

export const isAuthenticated = () => {
  const token = getAuthToken();
  return !!token;
};

// Helper function to get auth headers
const getAuthHeaders = () => {
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    "Accept": "application/json",
  };

  const token = getAuthToken();
  console.log('Getting auth headers, token:', token ? 'present' : 'missing');
  
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  return headers;
};

// JSON POST request
export const postReq = async (url: string, data: unknown, useAuth = false) => {
  if (USE_MOCK_DATA) {
    // Handle mock POST requests
    await new Promise(resolve => setTimeout(resolve, 300)); // Simulate delay

    if (url.includes('contact')) {
      toast.success('Contact form submitted successfully!');
      return { data: { message: 'Form submitted successfully' }, status: "200" };
    }

    if (url.includes('booking')) {
      toast.success('Booking request submitted successfully!');
      return {
        data: {
          booking_id: Math.random().toString(36).substring(2, 11),
          status: 'pending',
          message: 'Booking request submitted successfully'
        },
        status: "200"
      };
    }

    toast.success('Request processed successfully!');
    return { data: { message: 'Success' }, status: "200" };
  }

  // Django API implementation
  const cleanUrl = url.replace(/^\/+|\/+$/g, ''); // Remove leading/trailing slashes
  const fullUrl = `${API_BASE_URL}/${cleanUrl}/`;
  try {
    const headers = useAuth ? getAuthHeaders() : {
      "Content-Type": "application/json",
      "Accept": "application/json",
    };

    const response = await fetch(fullUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(data),
    });

    const responseData = await response.json();

    if (response.ok) {
      // For registration, don't show automatic success toast as AuthContext handles it
      if (!url.includes('auth/users')) {
        const message = responseData.message || 'Request processed successfully!';
        toast.success(message);
      }
      return { data: responseData, status: "200" };
    }

    // Handle Django validation errors
    let errorMsg = '';
    if (typeof responseData === 'object' && responseData !== null) {
      // Handle field-specific validation errors
      const errors = [];
      for (const [field, messages] of Object.entries(responseData)) {
        if (Array.isArray(messages)) {
          errors.push(`${field}: ${messages.join(', ')}`);
        } else if (typeof messages === 'string') {
          errors.push(`${field}: ${messages}`);
        }
      }
      errorMsg = errors.length > 0 ? errors.join('; ') : 'Validation error';
    } else {
      errorMsg = responseData.message || responseData.detail || `HTTP error ${response.status}`;
    }

    toast.error(errorMsg);
    return { status: String(response.status || "400"), message: errorMsg };
  } catch (error: any) {
    toast.error(error.message || 'An error occurred during POST request');
    return { status: "400", message: error.message || 'An error occurred' };
  }
};

// PATCH request
export const patchReq = async (url: string, data: unknown, useAuth = false) => {
  if (USE_MOCK_DATA) {
    // Handle mock PATCH requests
    await new Promise(resolve => setTimeout(resolve, 300)); // Simulate delay
    toast.success('Update successful!');
    return { data: { message: 'Update successful' }, status: "200" };
  }

  // Django API implementation
  const cleanUrl = url.replace(/^\/+|\/+$/g, ''); // Remove leading/trailing slashes
  const fullUrl = `${API_BASE_URL}/${cleanUrl}/`;
  try {
    const headers = useAuth ? getAuthHeaders() : {
      "Content-Type": "application/json",
      "Accept": "application/json",
    };

    const response = await fetch(fullUrl, {
      method: 'PATCH',
      headers,
      body: JSON.stringify(data),
    });

    const responseData = await response.json();

    if (response.ok) {
      // Don't show automatic success toast for profile updates as AuthContext handles it
      if (!url.includes('auth/users/me')) {
        const message = responseData.message || 'Update successful!';
        toast.success(message);
      }
      return { data: responseData, status: "200" };
    }

    // Handle Django validation errors
    let errorMsg = '';
    if (typeof responseData === 'object' && responseData !== null) {
      // Handle field-specific validation errors
      const errors = [];
      for (const [field, messages] of Object.entries(responseData)) {
        if (Array.isArray(messages)) {
          errors.push(`${field}: ${messages.join(', ')}`);
        } else if (typeof messages === 'string') {
          errors.push(`${field}: ${messages}`);
        }
      }
      errorMsg = errors.length > 0 ? errors.join('; ') : 'Validation error';
    } else {
      errorMsg = responseData.message || responseData.detail || `HTTP error ${response.status}`;
    }

    toast.error(errorMsg);
    return { status: String(response.status || "400"), message: errorMsg };
  } catch (error: any) {
    const errorMsg = error.message || 'Network error occurred';
    toast.error(errorMsg);
    return { status: "500", message: errorMsg };
  }
};

// GET request
export const getReq = async (url: string, useAuth = false) => {
  if (USE_MOCK_DATA) {
    // Handle mock GET requests
    await new Promise(resolve => setTimeout(resolve, 300)); // Simulate delay

    if (url.includes('products/')) {
      const params = new URLSearchParams(url.split('?')[1] || '');
      let filteredProducts = mockProducts;

      const category = params.get('category/');
      const search = params.get('search');

      if (category && category !== 'all') {
        filteredProducts = filteredProducts.filter(
          product => product.category.toLowerCase() === category.toLowerCase()
        );
      }

      if (search) {
        filteredProducts = filteredProducts.filter(
          product =>
            product.name.toLowerCase().includes(search.toLowerCase()) ||
            product.description.toLowerCase().includes(search.toLowerCase())
        );
      }

      return { success: true, data: filteredProducts };
    }
    
    if (url.includes('services/')) {
      const params = new URLSearchParams(url.split('?')[1] || '');
      let filteredCategories = mockServiceCategories;
      
      const popular_only = params.get('popular_only');
      if (popular_only === 'true') {
        filteredCategories = filteredCategories.map(category => ({
          ...category,
          services: category.services.filter(service => service.popular)
        }));
      }
      
      return { success: true, data: filteredCategories };
    }
    
    if (url.includes('testimonials/')) {
      const params = new URLSearchParams(url.split('?')[1] || '');
      let filteredTestimonials = mockTestimonials;
      
      const limit = params.get('limit');
      if (limit) {
        filteredTestimonials = filteredTestimonials.slice(0, parseInt(limit));
      }
      
      return { success: true, data: filteredTestimonials };
    }
    
    if (url.includes('gallery')) {
      const params = new URLSearchParams(url.split('?')[1] || '');
      let filteredImages = mockGalleryImages;
      
      const category = params.get('category');
      if (category && category !== 'all') {
        filteredImages = filteredImages.filter(
          image => image.category === category
        );
      }
      
      return { success: true, data: filteredImages };
    }
    
    // Handle individual item requests
    if (url.match(/\/products\/\d+/)) {
      const id = parseInt(url.split('/').pop() || '0');
      const product = mockProducts.find(p => p.id === id);
      if (!product) {
        return { success: false, error: 'Product not found' };
      }
      return { success: true, data: product };
    }
    
    if (url.match(/\/services\/\d+/)) {
      const id = parseInt(url.split('/').pop() || '0');
      let service: Service | undefined;
      
      for (const category of mockServiceCategories) {
        service = category.services.find(s => s.id === id);
        if (service) break;
      }
      
      if (!service) {
        return { success: false, error: 'Service not found' };
      }
      return { success: true, data: service };
    }
    
    return { success: true, data: [] };
  }

  // Django API implementation
  const cleanUrl = url.replace(/^\/+|\/+$/g, ''); // Remove leading/trailing slashes
  const fullUrl = url.startsWith('http') ? url : `${API_BASE_URL}/${cleanUrl}/`;
  try {
    const headers = useAuth ? getAuthHeaders() : {
      "Accept": "application/json",
      "Content-Type": "application/json",
    };

    const response = await fetch(fullUrl, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      let errorText = response.statusText;
      try {
        const errorData = await response.json();
        errorText = errorData.detail || errorData.message || response.statusText;
      } catch (textError) {
      }
      toast.error(`HTTP error ${response.status}: ${errorText || 'Failed to fetch'}`);
      return { success: false, error: errorText };
    }

    const data = await response.json();
    return { success: true, data: data };
  } catch (error: any) {
    toast.error(error.message || 'An error occurred while fetching data.');
    return { success: false, error: error.message || 'An error occurred' };
  }
};

// Multipart/form-data POST request
export const postReqMultipart = async (url: string, formData: FormData) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate delay
    toast.success('File uploaded successfully!');
    return { data: { message: 'File uploaded successfully' }, status: "200" };
  }

  // Django API implementation
  const cleanUrl = url.replace(/^\/+|\/+$/g, ''); // Remove leading/trailing slashes
  const fullUrl = url.startsWith('http') ? url : `${API_BASE_URL}/${cleanUrl}/`;
  try {
    const response = await fetch(fullUrl, {
      method: 'POST',
      body: formData
    });

    const responseData = await response.json();

    if (response.ok) {
      const message = responseData.message || 'File uploaded successfully!';
      toast.success(message);
      return { data: responseData, status: "200" };
    }

    const errorMsg = responseData.message || responseData.detail || `HTTP error ${response.status}`;
    toast.error(errorMsg);
    return { status: String(response.status || "400"), message: errorMsg };

  } catch (error: any) {
    toast.error(error.message || 'An error occurred during multipart POST request');
    return { status: "400", message: error.message || 'An error occurred' };
  }
};

// Helper functions for specific endpoints with Django API paths

/**
 * Get all products (filtering done on frontend)
 * Django API: GET /api/products/products/
 */
export const getProducts = async () => {
  // Use the new backend endpoint
  return getProductsFromBackend();
};

/**
 * Get product details by ID
 * Django API: GET /api/products/products/{id}/
 */
export const getProductById = async (id: number) => {
  return getProductByIdFromBackend(id);
};

// Services functionality removed - not available in backend
// If you need services, implement them in the Django backend first

/**
 * Get all testimonials (filtering done on frontend)
 * Django API: GET /api/store/testimonials/
 */
export const getTestimonials = async () => {
  // Simple API call - all filtering done on frontend
  return getReq('store/testimonials');
};

/**
 * Get featured testimonials
 * Django API: GET /api/store/testimonials/featured/
 */
export const getFeaturedTestimonials = async () => {
  return getReq('store/testimonials/featured');
};

// Gallery functionality removed - not available in backend
// If you need gallery, implement it in the Django backend first

/**
 * Submit contact form
 * Django API: POST /api/contact/
 */
export const submitContactForm = async (formData: {
  name: string;
  email: string;
  phone: string;
  service: string;
  message: string;
}) => {
  return postReq('contact', formData);
};

/**
 * Create booking
 * Django API: POST /api/bookings/
 */
export const createBooking = async (bookingData: {
  service_id: number;
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  preferred_date: string;
  preferred_time: string;
  notes?: string;
}) => {
  return postReq('bookings', bookingData);
};

/**
 * Get booking details by reference
 * Django API: GET /api/bookings/{reference}/
 */
export const getBookingByReference = async (reference: string) => {
  return getReq(`bookings/${reference}`);
};

/**
 * Get hero slides
 * Django API: GET /api/store/hero-slides/
 */
export const getHeroSlides = async () => {
  const cleanUrl = 'store/hero-slides'.replace(/^\/+|\/+$/g, '');
  const fullUrl = `${API_BASE_URL}/${cleanUrl}/`;

  try {
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        "Accept": "application/json",
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      let errorText = response.statusText;
      try {
        const errorData = await response.json();
        errorText = errorData.detail || errorData.message || response.statusText;
      } catch (textError) {
      }
      return { success: false, error: errorText };
    }

    const data = await response.json();

    // Django returns hero slides as an array
    return { success: true, data: data };
  } catch (error: any) {
    return { success: false, error: error.message || 'An error occurred' };
  }
};

/**
 * Get organization information
 * Django API: GET /api/store/organizations/
 */
export const getOrganizationInfo = async () => {
  const cleanUrl = 'store/organizations'.replace(/^\/+|\/+$/g, '');
  const fullUrl = `${API_BASE_URL}/${cleanUrl}/`;

  try {
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        "Accept": "application/json",
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      let errorText = response.statusText;
      try {
        const errorData = await response.json();
        errorText = errorData.detail || errorData.message || response.statusText;
      } catch (textError) {
      }
      return { success: false, error: errorText };
    }

    const data = await response.json();

    // Django returns organization data as an array with one object
    // Extract the first object from the array
    const orgData = Array.isArray(data) ? data[0] : data;

    return { success: true, data: orgData };
  } catch (error: any) {
    return { success: false, error: error.message || 'An error occurred' };
  }
};

/**
 * Submit testimonial
 * Django API: POST /api/store/testimonials/
 */
export const submitTestimonial = async (testimonialData: {
  name: string;
  email?: string;
  rating: number;
  comment: string;
  service_name: string;
  service?: number;
}) => {
  return postReq('store/testimonials', testimonialData);
};

/**
 * Update booking status (admin only)
 * Django API: PUT /api/bookings/{reference}/
 */
export const updateBookingStatus = async (reference: string, statusData: {
  status: string;
  actual_date?: string;
  actual_time?: string;
  admin_notes?: string;
}) => {
  const fullUrl = `${API_BASE_URL}/bookings/${reference}/`;
  try {
    const response = await fetch(fullUrl, {
      method: 'PUT',
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
      },
      body: JSON.stringify(statusData),
    });

    const responseData = await response.json();

    if (response.ok) {
      const message = responseData.message || 'Booking updated successfully!';
      toast.success(message);
      return { data: responseData, status: "200" };
    }

    const errorMsg = responseData.message || responseData.detail || `HTTP error ${response.status}`;
    toast.error(errorMsg);
    return { status: String(response.status || "400"), message: errorMsg };
  } catch (error: any) {
    toast.error(error.message || 'An error occurred while updating booking');
    return { status: "400", message: error.message || 'An error occurred' };
  }
};

/**
 * Get team members
 * Django API: GET /api/team-members/
 */
export const getTeamMembers = async () => {
  return getReq('team-members');
};

/**
 * Get achievements
 * Django API: GET /api/achievements/
 */
export const getAchievements = async () => {
  return getReq('achievements');
};

/**
 * Get about page content
 * Django API: GET /api/about-content/
 */
export const getAboutContent = async () => {
  return getReq('about-content');
};

/**
 * Get about page content by type
 * Django API: GET /api/about-content/by_type/?type={type}
 */
export const getAboutContentByType = async (type: string) => {
  return getReq(`about-content/by_type/?type=${type}`);
};

// ==================== AUTHENTICATION API ====================

/**
 * User registration
 * Django API: POST /api/auth/users/
 */
export const registerUser = async (userData: {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone?: string;
}) => {
  // Prepare data for Djoser - add re_password and include phone if provided
  const registrationData = {
    email: userData.email,
    password: userData.password,
    re_password: userData.password, // Djoser requires password confirmation
    first_name: userData.first_name,
    last_name: userData.last_name,
    ...(userData.phone && { phone: userData.phone }) // Include phone if provided
  };

  return postReq('auth/users', registrationData);
};

/**
 * User login
 * Django API: POST /api/auth/jwt/create/
 */
export const loginUser = async (credentials: {
  email: string;
  password: string;
}) => {
  const response = await postReq('auth/jwt/create', credentials);
  if (response.status === "200" && response.data?.access) {
    setAuthToken(response.data.access);
  }
  return response;
};

/**
 * Google OAuth login
 * Django API: POST /api/account/verify-google/
 */
export const googleLogin = async (credential: string) => {
  try {
    // Send the Google credential to our backend for verification
    const response = await postReq('account/verify-google', {
      credential: credential
    });
    
    if (response.status === "200" && response.data?.access) {
      setAuthToken(response.data.access);
      if (response.data.refresh) {
        localStorage.setItem('refreshToken', response.data.refresh);
      }
    }
    return response;
  } catch (error: any) {
    console.error('Google OAuth error:', error);
    return { status: "400", message: error.message || 'Google OAuth failed' };
  }
};

/**
 * Logout user
 */
export const logoutUser = async () => {
  try {
    if (authToken) {
      await postReq('auth/jwt/blacklist', { refresh: localStorage.getItem('refreshToken') }, true);
    }
  } catch (error) {
    console.error('Logout error:', error);
  } finally {
    setAuthToken(null);
    localStorage.removeItem('refreshToken');
  }
};

/**
 * Get current user profile
 * Django API: GET /api/auth/users/me/
 */
export const getCurrentUser = async () => {
  return getReq('auth/users/me', true);
};

/**
 * Update user profile
 * Django API: PATCH /api/auth/users/me/
 */
export const updateUserProfile = async (userData: {
  first_name?: string;
  last_name?: string;
  phone?: string;
}) => {
  return patchReq('auth/users/me', userData, true);
};

/**
 * Change password
 * Django API: POST /api/auth/users/set_password/
 */
export const changePassword = async (passwordData: {
  current_password: string;
  new_password: string;
  re_new_password: string;
}) => {
  return postReq('auth/users/set_password', passwordData, true);
};

/**
 * Reset password request
 * Django API: POST /api/auth/users/reset_password/
 */
export const resetPassword = async (email: string) => {
  return postReq('auth/users/reset_password', { email });
};

/**
 * Reset password confirm
 * Django API: POST /api/auth/users/reset_password_confirm/
 */
export const resetPasswordConfirm = async (resetData: {
  uid: string;
  token: string;
  new_password: string;
  re_new_password: string;
}) => {
  return postReq('auth/users/reset_password_confirm', resetData);
};

// ==================== PRODUCTS API ====================

/**
 * Get all products with Django backend
 * Django API: GET /api/products/products/
 */
export const getProductsFromBackend = async () => {
  return getReq('products/products');
};

/**
 * Get product by ID from Django backend
 * Django API: GET /api/products/products/{id}/
 */
export const getProductByIdFromBackend = async (id: number) => {
  return getReq(`products/products/${id}`);
};

/**
 * Get product categories
 * Django API: GET /api/products/categories/
 */
export const getProductCategories = async () => {
  return getReq('products/categories');
};

/**
 * Get product reviews
 * Django API: GET /api/products/reviews/
 */
export const getProductReviews = async (productId?: number) => {
  const url = productId ? `products/reviews/?product=${productId}` : 'products/reviews';
  return getReq(url);
};

/**
 * Submit product review
 * Django API: POST /api/products/reviews/
 */
export const submitProductReview = async (reviewData: {
  product: number;
  rating: number;
  comment: string;
}) => {
  return postReq('products/reviews', reviewData, true);
};

// ==================== CART API ====================

/**
 * Get user's cart
 * Django API: GET /api/carts/carts/
 */
export const getUserCart = async () => {
  return getReq('carts/carts', true);
};

/**
 * Create cart
 * Django API: POST /api/carts/carts/
 */
export const createCart = async (cartData: { cart_id: string }) => {
  return postReq('carts/carts', cartData, true);
};

/**
 * Get cart items
 * Django API: GET /api/carts/cart-items/
 */
export const getCartItems = async () => {
  return getReq('carts/cart-items', true);
};

/**
 * Add item to cart
 * Django API: POST /api/carts/cart-items/
 */
export const addToCart = async (itemData: {
  product_id: number;
  quantity: number;
  variation_ids?: number[];
}) => {
  return postReq('carts/cart-items', itemData, true);
};

/**
 * Update cart item
 * Django API: PATCH /api/carts/cart-items/{id}/
 */
export const updateCartItem = async (itemId: number, updateData: {
  quantity?: number;
}) => {
  const cleanUrl = `carts/cart-items/${itemId}`.replace(/^\/+|\/+$/g, '');
  const fullUrl = `${API_BASE_URL}/${cleanUrl}/`;

  try {
    const response = await fetch(fullUrl, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      body: JSON.stringify(updateData),
    });

    const responseData = await response.json();

    if (response.ok) {
      return { data: responseData, status: "200" };
    }

    const errorMsg = responseData.message || responseData.detail || `HTTP error ${response.status}`;
    toast.error(errorMsg);
    return { status: String(response.status || "400"), message: errorMsg };
  } catch (error: any) {
    toast.error(error.message || 'An error occurred while updating cart item');
    return { status: "400", message: error.message || 'An error occurred' };
  }
};

/**
 * Remove item from cart
 * Django API: DELETE /api/carts/cart-items/{id}/
 */
export const removeFromCart = async (itemId: number) => {
  const cleanUrl = `carts/cart-items/${itemId}`.replace(/^\/+|\/+$/g, '');
  const fullUrl = `${API_BASE_URL}/${cleanUrl}/`;

  try {
    const response = await fetch(fullUrl, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });

    if (response.ok) {
      toast.success('Item removed from cart');
      return { status: "200" };
    }

    const errorMsg = `HTTP error ${response.status}`;
    toast.error(errorMsg);
    return { status: String(response.status || "400"), message: errorMsg };
  } catch (error: any) {
    toast.error(error.message || 'An error occurred while removing cart item');
    return { status: "400", message: error.message || 'An error occurred' };
  }
};

// ==================== ORDERS API ====================

/**
 * Get payment methods
 * Django API: GET /api/orders/payment-methods/
 */
export const getPaymentMethods = async () => {
  return getReq('orders/payment-methods');
};

/**
 * Create order
 * Django API: POST /api/orders/orders/
 */
export const createOrder = async (orderData: {
  first_name: string;
  last_name: string;
  phone: string;
  email: string;
  state: string;
  area: string;
  address: string;
  order_note?: string;
  payment_method?: number;
}) => {
  return postReq('orders/orders', orderData, true);
};

/**
 * Get user orders
 * Django API: GET /api/orders/orders/
 */
export const getUserOrders = async () => {
  return getReq('orders/orders', true);
};

/**
 * Get order by ID
 * Django API: GET /api/orders/orders/{id}/
 */
export const getOrderById = async (orderId: number) => {
  return getReq(`orders/orders/${orderId}`, true);
};

/**
 * Get order products
 * Django API: GET /api/orders/order-products/
 */
export const getOrderProducts = async (orderId?: number) => {
  const url = orderId ? `orders/order-products/?order=${orderId}` : 'orders/order-products';
  return getReq(url, true);
};

/**
 * Create payment
 * Django API: POST /api/orders/payments/
 */
export const createPayment = async (paymentData: {
  payment_id: string;
  payment_method: number;
}) => {
  return postReq('orders/payments', paymentData, true);
};
